package com.simfun.sgf.thread2;

import com.lmax.disruptor.BlockingWaitStrategy;
import com.lmax.disruptor.WaitStrategy;
import com.lmax.disruptor.dsl.ProducerType;
import com.simfun.sgf.thread2.disruptor.DisruptorQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Constructor;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

/**
 * 用单线程来执行某些任务
 */
public abstract class SingleThreadDisruptorTaskWorker<T> {
    protected final static Logger log = LoggerFactory.getLogger(SingleThreadDisruptorTaskWorker.class);

    /**
     * disruptor队列
     */
    private DisruptorQueue<T> queue;


    private static final int STATE_INIT = 0;

    private static final int STATE_START = 1;

    private static final int STATE_STOP = 2;

    private static final int BUSY_QUEUE_NUM = 10;

    private volatile int state = STATE_INIT;

    private static final int DEFAULT_BUFFER_SIZE = 0b1 << 20;

    private static final int DEFAULT_SHUTDOWN_WAIT_TIME = 5000;

    private String name;

    private int shutdownWaitTime;

    private int bufferSize;

    private WaitStrategy waitStrategy;

    private ThreadFactory threadFactory;

    public long getTaskSize() {
        return bufferSize - queue.remainingCapacity();
    }

    protected SingleThreadDisruptorTaskWorker() {
    }

    public void start() {
        if (state != STATE_INIT) {
            throw new IllegalStateException(name + " state is illegal. expected=" + STATE_INIT + ", actual=" + state);
        }

        this.state = STATE_START;
        queue = new DisruptorQueue<>(bufferSize, waitStrategy, this::execute, ProducerType.MULTI, threadFactory);

        if (log.isInfoEnabled()) {
            log.info("{} is started", name);
        }
    }

    public void stop() {
        if (this.state != STATE_START) {
            return;
        }
        this.state = STATE_STOP;

        try {
            queue.shutdown(shutdownWaitTime, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("Failed to stop {}", name, e);
        }

        if (log.isInfoEnabled()) {
            log.info("{} is stopped", name);
        }
    }

    protected void addTask(T task) {
        if (this.state == STATE_START) {
            queue.produce(task);
        } else {
            throw new RejectedExecutionException("Worker has already been shutdown");
        }
    }

    protected abstract void execute(T task, Object... attach);

    public static <T> Builder <T> newBuilder(Class<? extends SingleThreadDisruptorTaskWorker<T>> workerClass) {
        return new <T> Builder<T>(workerClass);
    }

    public static class Builder<T> {
        private final Class<? extends SingleThreadDisruptorTaskWorker<T>> workerClass;

        private String name;

        private int shutdownWaitTime;

        private int bufferSize;

        private WaitStrategy waitStrategy;

        private ThreadFactory threadFactory;

        private Builder(Class<? extends SingleThreadDisruptorTaskWorker<T>> workerClass) {
            this.workerClass = workerClass;
        }

        public Builder<T> setName(String name) {
            this.name = name;
            return this;
        }

        public Builder<T> setShutdownWaitTime(int shutdownWaitTime) {
            this.shutdownWaitTime = shutdownWaitTime;
            return this;
        }

        public Builder<T> setBufferSize(int bufferSize) {
            this.bufferSize = bufferSize;
            return this;
        }

        public Builder<T> setWaitStrategy(WaitStrategy waitStrategy) {
            this.waitStrategy = waitStrategy;
            return this;
        }

        public Builder<T> setThreadFactory(ThreadFactory threadFactory) {
            this.threadFactory = threadFactory;
            return this;
        }

        public SingleThreadDisruptorTaskWorker<T> build() {
            try {
                if (name == null || name.isEmpty()) {
                    name = getClass().getSimpleName();
                }

                if (shutdownWaitTime <= 0) {
                    shutdownWaitTime = DEFAULT_SHUTDOWN_WAIT_TIME;
                }

                if (bufferSize <= 0) {
                    bufferSize = DEFAULT_BUFFER_SIZE;
                }

                if (waitStrategy == null) {
                    waitStrategy = new BlockingWaitStrategy();
                }

                Constructor<? extends SingleThreadDisruptorTaskWorker<T>> constructor = workerClass.
                        getDeclaredConstructor();
                constructor.setAccessible(true);
                SingleThreadDisruptorTaskWorker<T> worker = constructor.newInstance();
                worker.name = name;
                worker.shutdownWaitTime = shutdownWaitTime;
                worker.bufferSize = bufferSize;
                worker.waitStrategy = waitStrategy;
                worker.threadFactory = threadFactory;
                return worker;
            } catch (Throwable t) {
                log.error("创建disruptor单线程队列异常！！", t);
                return null;
            }
        }
    }
}
