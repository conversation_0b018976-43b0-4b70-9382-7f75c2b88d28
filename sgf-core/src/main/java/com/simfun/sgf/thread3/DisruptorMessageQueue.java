package com.simfun.sgf.thread3;

import com.lmax.disruptor.EventFactory;
import com.lmax.disruptor.RingBuffer;
import com.lmax.disruptor.TimeoutException;
import com.lmax.disruptor.dsl.Disruptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 基于Disruptor的现代化消息队列实现
 * 
 * @param <T> 消息类型
 * <AUTHOR>
 */
public final class DisruptorMessageQueue<T> implements MessageQueue<T> {
    
    private static final Logger log = LoggerFactory.getLogger(DisruptorMessageQueue.class);
    
    private final QueueConfig<T> config;
    private final AtomicReference<QueueState> state = new AtomicReference<>(QueueState.CREATED);
    private final AtomicLong producedCount = new AtomicLong(0);
    private final AtomicLong consumedCount = new AtomicLong(0);
    private final Instant startTime = Instant.now();
    private final AtomicReference<Instant> lastActivityTime = new AtomicReference<>(startTime);
    
    private volatile Disruptor<MutableEventContainer<T>> disruptor;
    private volatile RingBuffer<MutableEventContainer<T>> ringBuffer;
    
    /**
     * 构造函数
     * 
     * @param config 队列配置
     */
    public DisruptorMessageQueue(QueueConfig<T> config) {
        this.config = config;
    }
    
    @Override
    public void start() {
        if (!state.compareAndSet(QueueState.CREATED, QueueState.STARTING)) {
            throw new IllegalStateException("Queue already started or in invalid state: " + state.get());
        }
        
        try {
            initializeDisruptor();
            state.set(QueueState.RUNNING);
            
            log.info("DisruptorMessageQueue [{}] started with buffer size: {}", 
                    config.name().orElse("unnamed"), config.bufferSize());
        } catch (Exception e) {
            state.set(QueueState.ERROR);
            log.error("Failed to start DisruptorMessageQueue", e);
            throw new RuntimeException("Failed to start queue", e);
        }
    }
    
    private void initializeDisruptor() {
        // 事件工厂
        EventFactory<MutableEventContainer<T>> eventFactory = MutableEventContainer::new;
        
        // 线程工厂
        ThreadFactory threadFactory = config.threadFactory()
            .orElseGet(() -> Thread.ofVirtual()
                .name(config.name().orElse("queue") + "-", 0)
                .factory());
        
        // 创建Disruptor
        this.disruptor = new Disruptor<>(
            eventFactory,
            config.bufferSize(),
            threadFactory,
            config.producerType(),
            config.waitStrategy()
        );
        
        // 设置事件处理器
        this.disruptor.handleEventsWith(this::handleEvent);
        
        // 启动Disruptor
        this.disruptor.start();
        this.ringBuffer = disruptor.getRingBuffer();
        
        log.debug("Disruptor initialized successfully");
    }
    
    private void handleEvent(MutableEventContainer<T> container, long sequence, boolean endOfBatch) {
        T message = container.getAndClear();
        if (message != null) {
            try {
                config.processor().accept(message, sequence, endOfBatch);
                consumedCount.incrementAndGet();
                lastActivityTime.set(Instant.now());
            } catch (Exception e) {
                log.error("Error processing message: {}", message, e);
            }
        }
    }
    
    @Override
    public void submit(T message) {
        if (!state.get().canAcceptTasks()) {
            throw new IllegalStateException("Queue is not accepting tasks: " + state.get());
        }
        
        long sequence = ringBuffer.next();
        try {
            MutableEventContainer<T> event = ringBuffer.get(sequence);
            event.setValue(message);
            
            producedCount.incrementAndGet();
            lastActivityTime.set(Instant.now());
        } finally {
            ringBuffer.publish(sequence);
        }
    }
    
    @Override
    public CompletableFuture<Void> submitAsync(T message) {
        return CompletableFuture.runAsync(() -> submit(message));
    }
    
    @Override
    public boolean trySubmit(T message) {
        if (!state.get().canAcceptTasks()) {
            return false;
        }
        
        if (ringBuffer.remainingCapacity() <= 0) {
            return false;
        }
        
        try {
            submit(message);
            return true;
        } catch (Exception e) {
            log.debug("Failed to submit message", e);
            return false;
        }
    }
    
    @Override
    public void shutdown(Duration timeout) throws InterruptedException {
        if (!state.compareAndSet(QueueState.RUNNING, QueueState.STOPPING)) {
            return; // Already stopping or stopped
        }
        
        try {
            if (disruptor != null) {
                disruptor.shutdown(timeout.toMillis(), TimeUnit.MILLISECONDS);
            }
            state.set(QueueState.STOPPED);
            
            log.info("DisruptorMessageQueue [{}] stopped gracefully", 
                    config.name().orElse("unnamed"));
        } catch (TimeoutException e) {
            log.warn("Timeout while stopping queue, forcing shutdown");
            shutdownNow();
            throw new InterruptedException("Shutdown timeout");
        }
    }
    
    @Override
    public void shutdownNow() {
        state.set(QueueState.STOPPING);
        if (disruptor != null) {
            disruptor.halt();
        }
        state.set(QueueState.STOPPED);
        
        log.info("DisruptorMessageQueue [{}] stopped immediately", 
                config.name().orElse("unnamed"));
    }
    
    @Override
    public long remainingCapacity() {
        return ringBuffer != null ? ringBuffer.remainingCapacity() : 0;
    }
    
    @Override
    public QueueMetrics getMetrics() {
        return new QueueMetrics(
            producedCount.get(),
            consumedCount.get(),
            remainingCapacity(),
            state.get(),
            startTime,
            lastActivityTime.get()
        );
    }
    
    @Override
    public QueueState getState() {
        return state.get();
    }
} 