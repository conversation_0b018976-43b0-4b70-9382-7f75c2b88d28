package com.simfun.sgf.thread3;

import java.util.Optional;

/**
 * 事件容器，使用Record替代原来的DisruptorEvent
 * 
 * @param <T> 事件数据类型
 * @param value 事件值
 * <AUTHOR>
 */
public record EventContainer<T>(T value) {
    
    /**
     * 创建包含值的容器
     * 
     * @param value 值
     * @param <T> 值类型
     * @return 事件容器
     */
    public static <T> EventContainer<T> of(T value) {
        return new EventContainer<>(value);
    }
    
    /**
     * 创建空容器
     * 
     * @param <T> 值类型
     * @return 空容器
     */
    public static <T> EventContainer<T> empty() {
        return new EventContainer<>(null);
    }
    
    /**
     * 获取可选值
     * 
     * @return Optional包装的值
     */
    public Optional<T> getValue() {
        return Optional.ofNullable(value);
    }
    
    /**
     * 检查是否为空
     * 
     * @return true if empty
     */
    public boolean isEmpty() {
        return value == null;
    }
    
    /**
     * 检查是否有值
     * 
     * @return true if present
     */
    public boolean hasValue() {
        return value != null;
    }
} 