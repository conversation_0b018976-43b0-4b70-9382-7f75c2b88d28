package com.simfun.sgf.thread3;

import com.lmax.disruptor.BlockingWaitStrategy;
import com.lmax.disruptor.dsl.ProducerType;

import java.time.Duration;
import java.util.concurrent.ThreadFactory;

/**
 * 任务工作器工厂类，提供便捷的创建方法
 * 
 * <AUTHOR>
 */
public final class TaskWorkers {
    
    private TaskWorkers() {
        // 工具类，禁止实例化
    }
    
    /**
     * 创建单线程任务工作器构建器
     * 
     * @param <T> 任务类型
     * @return 构建器
     */
    public static <T> SingleThreadBuilder<T> singleThread() {
        return new SingleThreadBuilder<>();
    }
    
    /**
     * 创建多线程任务工作器构建器
     * 
     * @param <T> 任务类型
     * @return 构建器
     */
    public static <T> MultiThreadBuilder<T> multiThread() {
        return new MultiThreadBuilder<>();
    }
    
    /**
     * 快速创建单线程处理器并处理任务
     * 
     * @param name 处理器名称
     * @param processor 处理逻辑
     * @param tasks 任务列表
     * @param <T> 任务类型
     */
    @SafeVarargs
    public static <T> void processInSingleThread(String name, TaskProcessor<T> processor, T... tasks) {
        try (var worker = singleThread()
                .name(name)
                .processor(processor)
                .build()) {
            
            worker.start();
            for (T task : tasks) {
                worker.submit(task);
            }
            
            // 等待处理完成
            while (worker.getMetrics().pendingTasks() > 0) {
                Thread.onSpinWait();
            }
        }
    }
    
    /**
     * 单线程构建器
     * 
     * @param <T> 任务类型
     */
    public static class SingleThreadBuilder<T> {
        private String name = "single-thread-worker";
        private int bufferSize = 1 << 16;
        private TaskProcessor<T> processor = TaskProcessor.<T>noOp();
        private Duration shutdownTimeout = Duration.ofSeconds(5);
        private ThreadFactory threadFactory;
        
        public SingleThreadBuilder<T> name(String name) {
            this.name = name;
            return this;
        }
        
        public SingleThreadBuilder<T> bufferSize(int bufferSize) {
            this.bufferSize = bufferSize;
            return this;
        }

        public SingleThreadBuilder<T> processor(TaskProcessor<T> processor) {
            this.processor = processor;
            return this;
        }
        
        public SingleThreadBuilder<T> shutdownTimeout(Duration timeout) {
            this.shutdownTimeout = timeout;
            return this;
        }
        
        public SingleThreadBuilder<T> threadFactory(ThreadFactory threadFactory) {
            this.threadFactory = threadFactory;
            return this;
        }
        
        /**
         * 使用阻塞等待策略（默认）
         */
        public SingleThreadBuilder<T> blockingWait() {
            // 默认就是阻塞等待策略
            return this;
        }
        
        /**
         * 使用自旋等待策略（低延迟但高CPU）
         */
        public SingleThreadBuilder<T> yieldingWait() {
            return this;
        }
        
        /**
         * 使用睡眠等待策略（平衡）
         */
        public SingleThreadBuilder<T> sleepingWait() {
            return this;
        }
        
        public MessageQueue<T> build() {
            var config = QueueConfig.<T>builder()
                .bufferSize(bufferSize)
                .processor(processor)
                .producerType(ProducerType.SINGLE)
                .name(name)
                .shutdownTimeout(shutdownTimeout)
                .threadFactory(threadFactory)
                .waitStrategy(new BlockingWaitStrategy())
                .build();
                
            return new DisruptorMessageQueue<>(config);
        }
    }
    
    /**
     * 多线程构建器
     * 
     * @param <T> 任务类型
     */
    public static class MultiThreadBuilder<T> {
        private String name = "multi-thread-worker";
        private int bufferSize = 1 << 16;
        private TaskProcessor<T> processor = TaskProcessor.<T>noOp();
        private Duration shutdownTimeout = Duration.ofSeconds(5);
        private ThreadFactory threadFactory;
        
        public MultiThreadBuilder<T> name(String name) {
            this.name = name;
            return this;
        }
        
        public MultiThreadBuilder<T> bufferSize(int bufferSize) {
            this.bufferSize = bufferSize;
            return this;
        }
        
        public MultiThreadBuilder<T> processor(TaskProcessor<T> processor) {
            this.processor = processor;
            return this;
        }
        
        public MultiThreadBuilder<T> shutdownTimeout(Duration timeout) {
            this.shutdownTimeout = timeout;
            return this;
        }
        
        public MultiThreadBuilder<T> threadFactory(ThreadFactory threadFactory) {
            this.threadFactory = threadFactory;
            return this;
        }
        
        /**
         * 使用阻塞等待策略（默认）
         */
        public MultiThreadBuilder<T> blockingWait() {
            return this;
        }
        
        /**
         * 使用自旋等待策略（低延迟但高CPU）
         */
        public MultiThreadBuilder<T> yieldingWait() {
            return this;
        }
        
        /**
         * 使用睡眠等待策略（平衡）
         */
        public MultiThreadBuilder<T> sleepingWait() {
            return this;
        }
        
        public MessageQueue<T> build() {
            var config = QueueConfig.<T>builder()
                .bufferSize(bufferSize)
                .processor(processor)
                .producerType(ProducerType.MULTI)
                .name(name)
                .shutdownTimeout(shutdownTimeout)
                .threadFactory(threadFactory)
                .waitStrategy(new BlockingWaitStrategy())
                .build();
                
            return new DisruptorMessageQueue<>(config);
        }
    }
} 