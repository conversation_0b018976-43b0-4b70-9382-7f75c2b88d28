package com.simfun.sgf.thread3;

import java.util.function.BiConsumer;

/**
 * 现代化的任务处理器函数式接口
 * 
 * @param <T> 任务类型
 * <AUTHOR>
 */
@FunctionalInterface
public interface TaskProcessor<T> {
    
    /**
     * 处理任务
     * 
     * @param task 任务对象
     * @param attach 附加参数
     */
    void accept(T task, Object... attach);
    
    /**
     * 链式组合处理器
     * 
     * @param after 后续处理器
     * @return 组合后的处理器
     */
    default TaskProcessor<T> andThen(TaskProcessor<T> after) {
        return (task, attach) -> {
            this.accept(task, attach);
            after.accept(task, attach);
        };
    }
    
    /**
     * 空操作处理器
     * 
     * @param <T> 任务类型
     * @return 空处理器
     */
    static <T> TaskProcessor<T> noOp() {
        return (task, attach) -> {};
    }
    
    /**
     * 创建任务处理器
     * 
     * @param processor 处理逻辑
     * @param <T> 任务类型
     * @return 任务处理器
     */
    static <T> TaskProcessor<T> of(BiConsumer<T, Object[]> processor) {
        return processor::accept;
    }
    
    /**
     * 创建简单任务处理器（忽略附加参数）
     * 
     * @param processor 处理逻辑
     * @param <T> 任务类型
     * @return 任务处理器
     */
    static <T> TaskProcessor<T> simple(java.util.function.Consumer<T> processor) {
        return (task, attach) -> processor.accept(task);
    }
} 