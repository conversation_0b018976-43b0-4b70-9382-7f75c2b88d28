# 现代化任务处理框架 (thread3)

基于Java 21特性和函数式编程范式重构的高性能任务处理框架。

## 核心特性

- ✅ **Java 21现代语法**: Record、Sealed接口、Virtual Threads、Pattern Matching
- ✅ **函数式编程**: Lambda表达式、方法引用、链式调用
- ✅ **类型安全**: 强类型、Optional、泛型
- ✅ **零拷贝性能**: 基于Disruptor的高性能队列
- ✅ **资源自动管理**: AutoCloseable、try-with-resources
- ✅ **内置监控**: 实时指标、状态管理
- ✅ **异步支持**: CompletableFuture、非阻塞操作

## 快速开始

### 1. 简单任务处理

```java
// 最简单的使用方式
TaskWorkers.processInSingleThread(
    "my-processor",
    task -> System.out.println("处理: " + task),
    "任务1", "任务2", "任务3"
);
```

### 2. 单线程工作器

```java
try (var worker = TaskWorkers.<String>singleThread()
        .name("单线程处理器")
        .bufferSize(1024)
        .processor((task, attach) -> {
            System.out.println("处理任务: " + task);
        })
        .build()) {
    
    worker.start();
    worker.submit("任务1");
    worker.submit("任务2");
    
    // 自动资源清理
}
```

### 3. 多生产者队列

```java
try (var worker = TaskWorkers.<Integer>multiThread()
        .name("多生产者队列")
        .processor((task, attach) -> processTask(task))
        .build()) {
    
    worker.start();
    
    // 多个线程并发提交
    CompletableFuture.allOf(
        CompletableFuture.runAsync(() -> submitTasks(worker, 1, 100)),
        CompletableFuture.runAsync(() -> submitTasks(worker, 101, 200)),
        CompletableFuture.runAsync(() -> submitTasks(worker, 201, 300))
    ).join();
}
```

## 核心API

### TaskProcessor - 任务处理器

```java
// 函数式接口，支持链式调用
TaskProcessor<String> validator = (task, attach) -> validate(task);
TaskProcessor<String> processor = (task, attach) -> process(task);
TaskProcessor<String> logger = (task, attach) -> log(task);

// 链式组合
TaskProcessor<String> combined = validator
    .andThen(processor)
    .andThen(logger);
```

### MessageQueue - 消息队列

```java
public sealed interface MessageQueue<T> extends AutoCloseable {
    void start();
    void submit(T message);
    CompletableFuture<Void> submitAsync(T message);
    boolean trySubmit(T message);
    QueueMetrics getMetrics();
    QueueState getState();
}
```

### 配置选项

```java
var worker = TaskWorkers.<String>singleThread()
    .name("自定义名称")
    .bufferSize(2048)              // 缓冲区大小(必须是2的幂)
    .shutdownTimeout(Duration.ofSeconds(10))  // 关闭超时
    .threadFactory(virtualThreadFactory())    // 虚拟线程工厂
    .blockingWait()                // 等待策略: blocking/yielding/sleeping
    .processor(myProcessor)        // 任务处理器
    .build();
```

## 性能优化

### 1. 虚拟线程支持

```java
// 自动使用虚拟线程(Java 21+)
var worker = TaskWorkers.<String>singleThread()
    .threadFactory(Thread.ofVirtual().name("worker-", 0).factory())
    .build();
```

### 2. 等待策略选择

```java
// 低延迟(高CPU占用)
.yieldingWait()

// 平衡性能
.sleepingWait()

// 低CPU占用(默认)
.blockingWait()
```

### 3. 缓冲区调优

```java
// 根据吞吐量需求调整(必须是2的幂)
.bufferSize(1024)    // 低吞吐量
.bufferSize(65536)   // 高吞吐量
```

## 监控和观测

### 1. 实时指标

```java
QueueMetrics metrics = worker.getMetrics();
System.out.println("已生产: " + metrics.totalProduced());
System.out.println("已消费: " + metrics.totalConsumed());
System.out.println("待处理: " + metrics.pendingTasks());
System.out.println("剩余容量: " + worker.remainingCapacity());
```

### 2. 状态监控

```java
QueueState state = worker.getState();
switch (state) {
    case RUNNING -> System.out.println("队列运行中");
    case STOPPING -> System.out.println("队列停止中");
    case STOPPED -> System.out.println("队列已停止");
    case ERROR -> System.out.println("队列错误");
}
```

## 异常处理

### 1. 优雅关闭

```java
try (var worker = TaskWorkers.<String>singleThread()
        .shutdownTimeout(Duration.ofSeconds(30))
        .build()) {
    // 工作代码
} // 自动优雅关闭，等待30秒
```

### 2. 任务处理异常

```java
TaskProcessor<String> safeProcessor = (task, attach) -> {
    try {
        dangerousProcess(task);
    } catch (Exception e) {
        log.error("处理失败: " + task, e);
        // 异常不会中断队列运行
    }
};
```

## 迁移指南

### 从旧版本迁移

```java
// 旧版本
public class OldWorker extends SingleThreadDisruptorTaskWorker<String> {
    @Override
    protected void execute(String task, Object... attach) {
        // 处理逻辑
    }
}

// 新版本
var newWorker = TaskWorkers.<String>singleThread()
    .processor((task, attach) -> {
        // 相同的处理逻辑
    })
    .build();
```

## 最佳实践

1. **资源管理**: 总是使用try-with-resources
2. **异常处理**: 在processor内部处理异常
3. **性能调优**: 根据实际负载调整缓冲区大小
4. **监控**: 定期检查队列指标和状态
5. **优雅关闭**: 设置合适的关闭超时时间

## 示例项目

参考 `TaskWorkerExamples.java` 获取完整的使用示例。 