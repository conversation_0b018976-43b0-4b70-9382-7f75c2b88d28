package com.simfun.sgf.thread3.examples;

import com.simfun.sgf.thread3.TaskProcessor;
import com.simfun.sgf.thread3.TaskWorkers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 现代化任务工作器使用示例
 * 
 * <AUTHOR>
 */
public class TaskWorkerExamples {
    
    private static final Logger log = LoggerFactory.getLogger(TaskWorkerExamples.class);
    
    /**
     * 示例1：简单的单线程任务处理
     */
    public static void singleThreadExample() {
        log.info("=== 单线程任务处理示例 ===");
        
        // 函数式风格定义处理逻辑
        TaskProcessor<String> processor = (task, attach) -> {
            log.info("处理任务: {}", task);
            // 模拟处理时间
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        };
        
        // 使用try-with-resources自动管理资源
        try (var worker = TaskWorkers.<String>singleThread()
                .name("demo-worker")
                .bufferSize(1024)
                .processor(processor)
                .build()) {
            
            worker.start();
            
            // 提交任务
            worker.submit("任务1");
            worker.submit("任务2");
            worker.submit("任务3");
            
            // 等待处理完成
            while (worker.getMetrics().pendingTasks() > 0) {
                log.info("待处理任务数: {}", worker.getMetrics().pendingTasks());
                Thread.sleep(50);
            }
            
            log.info("所有任务处理完成");
        } catch (Exception e) {
            log.error("处理失败", e);
        }
    }
    
    /**
     * 示例2：快速处理多个任务
     */
    public static void quickProcessExample() {
        log.info("=== 快速处理示例 ===");
        
        // 一行代码处理多个任务
        TaskWorkers.processInSingleThread(
            "quick-processor",
            TaskProcessor.simple(task -> log.info("快速处理: {}", task)),
            "任务A", "任务B", "任务C"
        );
    }
    
    /**
     * 示例3：链式处理器
     */
    public static void chainedProcessorExample() {
        log.info("=== 链式处理器示例 ===");
        
        TaskProcessor<String> validator = (task, attach) -> {
            if (task == null || task.trim().isEmpty()) {
                throw new IllegalArgumentException("任务不能为空");
            }
            log.info("验证通过: {}", task);
        };
        
        TaskProcessor<String> processor = (task, attach) -> {
            log.info("处理任务: {}", task);
        };
        
        TaskProcessor<String> logger = (task, attach) -> {
            log.info("记录日志: {}", task);
        };
        
        // 链式组合处理器
        TaskProcessor<String> combinedProcessor = validator
            .andThen(processor)
            .andThen(logger);
        
        try (var worker = TaskWorkers.<String>singleThread()
                .name("chained-worker")
                .processor(combinedProcessor)
                .build()) {
            
            worker.start();
            worker.submit("重要任务");
            
            // 等待完成
            Thread.sleep(500);
        } catch (Exception e) {
            log.error("处理失败", e);
        }
    }
    
    /**
     * 示例4：多生产者队列
     */
    public static void multiProducerExample() {
        log.info("=== 多生产者队列示例 ===");
        
        try (var worker = TaskWorkers.<Integer>multiThread()
                .name("multi-producer")
                .bufferSize(2048)
                .processor((task, attach) -> {
                    log.info("线程[{}]处理任务: {}", Thread.currentThread().getName(), task);
                })
                .build()) {
            
            worker.start();
            
            // 多个线程同时提交任务
            for (int i = 0; i < 3; i++) {
                final int threadId = i;
                new Thread(() -> {
                    for (int j = 0; j < 5; j++) {
                        worker.submit(threadId * 10 + j);
                    }
                }, "Producer-" + i).start();
            }
            
            // 等待处理完成
            Thread.sleep(1000);
        } catch (Exception e) {
            log.error("处理失败", e);
        }
    }
    
    /**
     * 示例5：异步提交和监控
     */
    public static void asyncAndMonitoringExample() {
        log.info("=== 异步提交和监控示例 ===");
        
        try (var worker = TaskWorkers.<String>singleThread()
                .name("async-worker")
                .processor((task, attach) -> {
                    log.info("异步处理: {}", task);
                    try {
                        Thread.sleep(200);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                })
                .build()) {
            
            worker.start();
            
            // 异步提交任务
            var future1 = worker.submitAsync("异步任务1");
            var future2 = worker.submitAsync("异步任务2");
            
            // 监控队列状态
            new Thread(() -> {
                while (worker.isRunning()) {
                    var metrics = worker.getMetrics();
                    log.info("队列状态 - 已生产:{}, 已消费:{}, 待处理:{}, 剩余容量:{}", 
                        metrics.totalProduced(), 
                        metrics.totalConsumed(),
                        metrics.pendingTasks(),
                        worker.remainingCapacity());
                    try {
                        Thread.sleep(300);
                    } catch (InterruptedException e) {
                        break;
                    }
                }
            }, "Monitor").start();
            
            // 等待异步任务完成
            future1.join();
            future2.join();
            
            Thread.sleep(1000);
        } catch (Exception e) {
            log.error("处理失败", e);
        }
    }
    
    public static void main(String[] args) {
        singleThreadExample();
        quickProcessExample();
        chainedProcessorExample();
        multiProducerExample();
        asyncAndMonitoringExample();
    }
} 