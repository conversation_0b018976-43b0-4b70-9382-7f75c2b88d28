# 方案1：兼容性单线程任务工作器 (thread4)

基于现代化内核但保持完全兼容API的单线程任务工作器。

## 核心特性

- ✅ **100%兼容**: 与现有`SingleThreadDisruptorTaskWorker`完全相同的API
- ✅ **零学习成本**: 无需修改现有代码即可迁移
- ✅ **现代化内核**: 内部使用Java 21特性和高性能队列
- ✅ **增强功能**: 支持AutoCloseable、虚拟线程等新特性
- ✅ **性能提升**: 基于新的MessageQueue实现

## 迁移方式

### 原有代码
```java
// 旧版本代码
import com.simfun.sgf.thread2.SingleThreadDisruptorTaskWorker;

public class MyWorker extends SingleThreadDisruptorTaskWorker<String> {
    @Override
    protected void execute(String task, Object... attach) {
        // 处理逻辑
        System.out.println("处理: " + task);
    }
}

// 使用方式
MyWorker worker = SingleThreadDisruptorTaskWorker
    .newBuilder(MyWorker.class)
    .setName("my-worker")
    .setBufferSize(1024)
    .setShutdownWaitTime(5000)
    .build();

worker.start();
worker.addTask("任务1");
worker.stop();
```

### 迁移后代码
```java
// 新版本代码 - 只需修改import语句！
import com.simfun.sgf.thread4.SingleThreadTaskWorker;

public class MyWorker extends SingleThreadTaskWorker<String> {
    @Override
    protected void execute(String task, Object... attach) {
        // 处理逻辑（完全相同）
        System.out.println("处理: " + task);
    }
}

// 使用方式（完全相同）
MyWorker worker = SingleThreadTaskWorker
    .newBuilder(MyWorker.class)
    .setName("my-worker")
    .setBufferSize(1024)
    .setShutdownWaitTime(5000)
    .build();

worker.start();
worker.addTask("任务1");
worker.stop();
```

## API对比

| 功能 | 旧版本 | 新版本 | 兼容性 |
|------|--------|--------|---------|
| **抽象类继承** | ✅ | ✅ | ✅ 完全相同 |
| **execute方法** | ✅ | ✅ | ✅ 完全相同 |
| **Builder模式** | ✅ | ✅ | ✅ 完全相同 |
| **start/stop** | ✅ | ✅ | ✅ 完全相同 |
| **addTask** | ✅ | ✅ | ✅ 完全相同 |
| **getTaskSize** | ✅ | ✅ | ✅ 完全相同 |
| **异常处理** | ✅ | ✅ | ✅ 完全相同 |
| **AutoCloseable** | ❌ | ✅ | ➕ 新增功能 |
| **虚拟线程支持** | ❌ | ✅ | ➕ 新增功能 |

## 完整使用示例

### 1. 基础使用（与旧版本完全相同）

```java
public class StringProcessor extends SingleThreadTaskWorker<String> {
    
    @Override
    protected void execute(String task, Object... attach) {
        log.info("处理字符串任务: {}", task);
        // 你的处理逻辑
    }
}

// 创建和使用
StringProcessor processor = SingleThreadTaskWorker
    .newBuilder(StringProcessor.class)
    .setName("string-processor")
    .setBufferSize(2048)
    .setShutdownWaitTime(10000)
    .build();

processor.start();
processor.addTask("Hello");
processor.addTask("World");
processor.stop();
```

### 2. 现代化用法（try-with-resources）

```java
// 利用AutoCloseable特性
try (var processor = SingleThreadTaskWorker
        .newBuilder(StringProcessor.class)
        .setName("auto-close-processor")
        .build()) {
    
    processor.start();
    processor.addTask("任务1");
    processor.addTask("任务2");
    
    // 等待处理完成
    while (processor.getTaskSize() > 0) {
        Thread.sleep(100);
    }
    
    // 自动调用close() -> stop()
}
```

### 3. 虚拟线程支持

```java
// 使用Java 21虚拟线程
var processor = SingleThreadTaskWorker
    .newBuilder(MyWorker.class)
    .setThreadFactory(Thread.ofVirtual().name("worker-", 0).factory())
    .build();
```

## 高级特性

### 1. 任务监控

```java
// 实时监控任务处理状态
public void monitorTasks(SingleThreadTaskWorker<String> worker) {
    new Thread(() -> {
        while (worker.getTaskSize() >= 0) {
            long taskCount = worker.getTaskSize();
            log.info("当前待处理任务数: {}", taskCount);
            
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                break;
            }
        }
    }, "TaskMonitor").start();
}
```

### 2. 批量任务处理

```java
public class BatchTaskWorker extends SingleThreadTaskWorker<List<String>> {
    
    @Override
    protected void execute(List<String> taskBatch, Object... attach) {
        log.info("批量处理 {} 个任务", taskBatch.size());
        
        for (String task : taskBatch) {
            // 处理单个任务
            processIndividualTask(task);
        }
    }
    
    private void processIndividualTask(String task) {
        // 单任务处理逻辑
    }
}
```

### 3. 错误处理和重试

```java
public class RobustTaskWorker extends SingleThreadTaskWorker<String> {
    private static final int MAX_RETRIES = 3;
    
    @Override
    protected void execute(String task, Object... attach) {
        int retries = 0;
        while (retries < MAX_RETRIES) {
            try {
                // 尝试处理任务
                processTask(task);
                return; // 成功则退出
                
            } catch (Exception e) {
                retries++;
                log.warn("任务处理失败，重试 {}/{}: {}", retries, MAX_RETRIES, e.getMessage());
                
                if (retries >= MAX_RETRIES) {
                    log.error("任务最终失败: {}", task, e);
                    // 可以将失败任务发送到死信队列
                }
            }
        }
    }
    
    private void processTask(String task) throws Exception {
        // 实际的任务处理逻辑
    }
}
```

## 性能优化建议

### 1. 缓冲区大小调优
```java
// 根据任务量调整缓冲区大小
.setBufferSize(1024)      // 低负载
.setBufferSize(16384)     // 中等负载  
.setBufferSize(65536)     // 高负载
```

### 2. 关闭超时配置
```java
// 根据任务复杂度调整关闭超时
.setShutdownWaitTime(1000)   // 简单任务
.setShutdownWaitTime(5000)   // 一般任务
.setShutdownWaitTime(30000)  // 复杂任务
```

## 迁移检查清单

- [ ] 修改import语句：`thread2` → `thread4`
- [ ] 类名修改：`SingleThreadDisruptorTaskWorker` → `SingleThreadTaskWorker`
- [ ] 测试所有功能是否正常工作
- [ ] 验证性能是否符合预期
- [ ] 考虑使用新增的AutoCloseable特性
- [ ] 考虑配置虚拟线程工厂（Java 21+）

## 常见问题

**Q: 迁移后性能会有变化吗？**
A: 一般情况下性能会有提升，因为内部使用了更现代化的实现。

**Q: 原有的配置和行为会改变吗？**
A: 不会，所有默认值和行为都保持不变。

**Q: 可以逐步迁移吗？**
A: 可以，新旧版本可以并存，逐个类进行迁移。

**Q: 遇到兼容性问题怎么办？**
A: 理论上不应该有兼容性问题，如有问题请及时反馈。

## 总结

方案1提供了**零学习成本**的迁移路径，让您可以：
- ✅ 立即享受现代化内核的性能提升
- ✅ 保持现有代码的稳定性
- ✅ 可选择性地使用新特性
- ✅ 降低迁移风险 