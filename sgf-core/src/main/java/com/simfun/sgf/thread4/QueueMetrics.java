package com.simfun.sgf.thread4;

import java.time.Instant;

/**
 * 队列运行指标
 * 
 * @param totalProduced 总生产数量
 * @param totalConsumed 总消费数量
 * @param remainingCapacity 剩余容量
 * @param currentState 当前状态
 * @param startTime 启动时间
 * @param lastActivityTime 最后活动时间
 * <AUTHOR>
 */
public record QueueMetrics(
    long totalProduced,
    long totalConsumed,
    long remainingCapacity,
    QueueState currentState,
    Instant startTime,
    Instant lastActivityTime
) {
    
    /**
     * 获取待处理任务数量
     * 
     * @return 待处理数量
     */
    public long pendingTasks() {
        return totalProduced - totalConsumed;
    }
    
    /**
     * 队列是否空闲
     * 
     * @return true if no pending tasks
     */
    public boolean isEmpty() {
        return pendingTasks() == 0;
    }
    
    /**
     * 创建空指标
     * 
     * @return 初始指标
     */
    public static QueueMetrics empty() {
        var now = Instant.now();
        return new QueueMetrics(0, 0, 0, QueueState.CREATED, now, now);
    }
    
    /**
     * 更新生产指标
     * 
     * @param count 生产数量
     * @return 新的指标
     */
    public QueueMetrics withProduced(long count) {
        return new QueueMetrics(
            totalProduced + count,
            totalConsumed,
            remainingCapacity,
            currentState,
            startTime,
            Instant.now()
        );
    }
    
    /**
     * 更新消费指标
     * 
     * @param count 消费数量
     * @return 新的指标
     */
    public QueueMetrics withConsumed(long count) {
        return new QueueMetrics(
            totalProduced,
            totalConsumed + count,
            remainingCapacity,
            currentState,
            startTime,
            Instant.now()
        );
    }
} 