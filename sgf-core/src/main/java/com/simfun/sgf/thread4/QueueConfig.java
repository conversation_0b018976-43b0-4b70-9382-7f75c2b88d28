package com.simfun.sgf.thread4;

import com.lmax.disruptor.BlockingWaitStrategy;
import com.lmax.disruptor.WaitStrategy;
import com.lmax.disruptor.dsl.ProducerType;
import com.simfun.sgf.thread3.TaskProcessor;

import java.time.Duration;
import java.util.Optional;
import java.util.concurrent.ThreadFactory;

/**
 * 队列配置记录类
 * 
 * @param <T> 消息类型
 * @param bufferSize 缓冲区大小
 * @param waitStrategy 等待策略
 * @param producerType 生产者类型
 * @param processor 任务处理器
 * @param threadFactory 线程工厂
 * @param name 队列名称
 * @param shutdownTimeout 关闭超时时间
 * <AUTHOR>
 */
public record QueueConfig<T>(
    int bufferSize,
    WaitStrategy waitStrategy,
    ProducerType producerType,
    TaskProcessor<T> processor,
    Optional<ThreadFactory> threadFactory,
    Optional<String> name,
    Duration shutdownTimeout
) {
    
    private static final int DEFAULT_BUFFER_SIZE = 1 << 16; // 64K
    private static final Duration DEFAULT_SHUTDOWN_TIMEOUT = Duration.ofSeconds(5);
    
    /**
     * 创建Builder
     * 
     * @param <T> 消息类型
     * @return Builder实例
     */
    public static <T> Builder<T> builder() {
        return new Builder<>();
    }
    
    /**
     * 创建默认配置
     * 
     * @param processor 处理器
     * @param <T> 消息类型
     * @return 默认配置
     */
    public static <T> QueueConfig<T> defaultConfig(TaskProcessor<T> processor) {
        return QueueConfig.<T>builder()
            .processor(processor)
            .build();
    }
    
    public static class Builder<T> {
        private int bufferSize = DEFAULT_BUFFER_SIZE;
        private WaitStrategy waitStrategy = new BlockingWaitStrategy();
        private ProducerType producerType = ProducerType.SINGLE;
        private TaskProcessor<T> processor = TaskProcessor.noOp();
        private ThreadFactory threadFactory;
        private String name;
        private Duration shutdownTimeout = DEFAULT_SHUTDOWN_TIMEOUT;
        
        public Builder<T> bufferSize(int bufferSize) {
            if (bufferSize <= 0 || (bufferSize & (bufferSize - 1)) != 0) {
                throw new IllegalArgumentException("Buffer size must be a positive power of 2");
            }
            this.bufferSize = bufferSize;
            return this;
        }
        
        public Builder<T> waitStrategy(WaitStrategy waitStrategy) {
            this.waitStrategy = waitStrategy;
            return this;
        }
        
        public Builder<T> producerType(ProducerType producerType) {
            this.producerType = producerType;
            return this;
        }
        
        public Builder<T> processor(TaskProcessor<T> processor) {
            this.processor = processor;
            return this;
        }
        
        public Builder<T> threadFactory(ThreadFactory threadFactory) {
            this.threadFactory = threadFactory;
            return this;
        }
        
        public Builder<T> name(String name) {
            this.name = name;
            return this;
        }
        
        public Builder<T> shutdownTimeout(Duration timeout) {
            this.shutdownTimeout = timeout;
            return this;
        }
        
        public QueueConfig<T> build() {
            if (processor == null) {
                throw new IllegalStateException("Processor cannot be null");
            }
            
            return new QueueConfig<>(
                bufferSize,
                waitStrategy,
                producerType,
                processor,
                Optional.ofNullable(threadFactory),
                Optional.ofNullable(name),
                shutdownTimeout
            );
        }
    }
} 