package com.simfun.sgf.thread4;

import java.util.Optional;

/**
 * 可变的事件容器，专用于Disruptor的RingBuffer
 * 虽然我们推崇不可变性，但Disruptor要求可变的事件对象以实现零GC
 * 
 * @param <T> 事件数据类型
 * <AUTHOR>
 */
public final class MutableEventContainer<T> {
    
    private volatile T value;
    
    /**
     * 默认构造函数
     */
    public MutableEventContainer() {
        this.value = null;
    }
    
    /**
     * 获取值
     * 
     * @return 当前值
     */
    public T getValue() {
        return value;
    }
    
    /**
     * 设置值
     * 
     * @param value 新值
     */
    public void setValue(T value) {
        this.value = value;
    }
    
    /**
     * 获取值并清空
     * 
     * @return 当前值
     */
    public T getAndClear() {
        T current = this.value;
        this.value = null;
        return current;
    }
    
    /**
     * 获取可选值
     * 
     * @return Optional包装的值
     */
    public Optional<T> getOptionalValue() {
        return Optional.ofNullable(value);
    }
    
    /**
     * 检查是否为空
     * 
     * @return true if empty
     */
    public boolean isEmpty() {
        return value == null;
    }
    
    /**
     * 检查是否有值
     * 
     * @return true if present
     */
    public boolean hasValue() {
        return value != null;
    }
    
    /**
     * 清空值
     */
    public void clear() {
        this.value = null;
    }
    
    @Override
    public String toString() {
        return "MutableEventContainer{value=" + value + '}';
    }
} 