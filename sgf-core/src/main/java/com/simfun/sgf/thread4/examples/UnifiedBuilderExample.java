package com.simfun.sgf.thread4.examples;

import com.simfun.sgf.thread4.TaskWorkers;
import com.simfun.sgf.thread4.SingleThreadTaskWorker;
import com.lmax.disruptor.SleepingWaitStrategy;

import java.time.Duration;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 方案3：统一Builder示例
 * 
 * <AUTHOR>
 */
public class UnifiedBuilderExample {
    
    public static void main(String[] args) throws InterruptedException {
        // 演示新的API设计
        modernApiExample();
        
        // 演示兼容性保持
        backwardCompatibilityExample();
        
        // 演示高级配置
        advancedConfigurationExample();
    }
    
    /**
     * 现代化API示例
     */
    private static void modernApiExample() {
        System.out.println("=== 现代化API示例 ===");
        
        // 单一生产者模式（推荐）
        try (var singleProducer = TaskWorkers.<String>singleProducer()
                .name("single-producer-demo")
                .processor((task, attach) -> 
                    System.out.println("处理任务: " + task))
                .build()) {
            
            singleProducer.start();
            singleProducer.submit("任务1");
            singleProducer.submit("任务2");
        }
        
        // 多个生产者模式
        try (var multiProducer = TaskWorkers.<String>multiProducer()
                .name("multi-producer-demo")
                .processor((task, attach) -> 
                    System.out.println("处理任务: " + task))
                .build()) {
            
            multiProducer.start();
            
            // 模拟多个线程提交任务
            for (int i = 0; i < 3; i++) {
                final int threadId = i;
                new Thread(() -> {
                    for (int j = 0; j < 5; j++) {
                        multiProducer.submit("线程" + threadId + "-任务" + j);
                    }
                }).start();
            }
            
            // 等待处理完成
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 向后兼容性示例
     */
    private static void backwardCompatibilityExample() {
        System.out.println("\n=== 向后兼容性示例 ===");
        
        // 使用已废弃但仍可用的方法
        try (var legacyWorker = TaskWorkers.<String>singleThread()
                .name("legacy-api")
                .processor((task, attach) -> 
                    System.out.println("兼容模式处理: " + task))
                .build()) {
            
            legacyWorker.start();
            legacyWorker.submit("兼容性测试");
        }
        
        // 传统的继承方式仍然有效
        var classicWorker = SingleThreadTaskWorker.newBuilder(MyTaskWorker.class)
            .setName("classic-worker")
            .build();
        
        classicWorker.start();
        classicWorker.addTask("传统API测试");
        classicWorker.stop();
    }
    
    /**
     * 高级配置示例
     */
    private static void advancedConfigurationExample() throws InterruptedException {
        System.out.println("\n=== 高级配置示例 ===");
        
        // 自定义线程工厂
        ThreadFactory customThreadFactory = new ThreadFactory() {
            private final AtomicInteger counter = new AtomicInteger(0);
            
            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r, "CustomWorker-" + counter.incrementAndGet());
                t.setDaemon(true);
                return t;
            }
        };
        
        CountDownLatch latch = new CountDownLatch(5);
        
        // 高性能配置
        try (var highPerfWorker = TaskWorkers.<Integer>singleProducer()
                .name("high-performance-worker")
                .bufferSize(1 << 18)  // 256K buffer
                .processor((task, attach) -> {
                    // 模拟处理
                    System.out.println("高性能处理: " + task);
                    latch.countDown();
                })
                .threadFactory(customThreadFactory)
                .shutdownTimeout(Duration.ofSeconds(10))
                .sleepingWait()  // 平衡延迟和CPU使用率
                .build()) {
            
            highPerfWorker.start();
            
            // 提交大量任务
            for (int i = 0; i < 5; i++) {
                highPerfWorker.submit(i);
            }
            
            // 等待所有任务完成
            latch.await();
            
            // 查看指标
            var metrics = highPerfWorker.getMetrics();
            System.out.println("待处理任务数: " + metrics.pendingTasks());
            System.out.println("总生产数: " + metrics.totalProduced());
            System.out.println("总消费数: " + metrics.totalConsumed());
        }
    }
    
    /**
     * 传统继承方式的工作器
     */
    private static class MyTaskWorker extends SingleThreadTaskWorker<String> {
        @Override
        protected void execute(String task, Object... attach) {
            System.out.println("传统方式处理: " + task);
        }
    }
} 