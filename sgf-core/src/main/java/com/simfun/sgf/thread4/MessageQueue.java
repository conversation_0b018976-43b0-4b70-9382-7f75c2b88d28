package com.simfun.sgf.thread4;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;

/**
 * 现代化的消息队列接口
 * 
 * @param <T> 消息类型
 */
public sealed interface MessageQueue<T> extends AutoCloseable permits DisruptorMessageQueue {
    
    /**
     * 启动队列
     */
    void start();
    
    /**
     * 同步提交消息
     * 
     * @param message 消息
     * @throws IllegalStateException 如果队列未运行
     */
    void submit(T message);
    
    /**
     * 异步提交消息
     * 
     * @param message 消息
     * @return 异步结果
     */
    CompletableFuture<Void> submitAsync(T message);
    
    /**
     * 尝试提交消息（非阻塞）
     * 
     * @param message 消息
     * @return true if successful
     */
    boolean trySubmit(T message);
    
    /**
     * 关闭队列
     * 
     * @param timeout 超时时间
     * @throws InterruptedException 中断异常
     */
    void shutdown(Duration timeout) throws InterruptedException;
    
    /**
     * 立即关闭（不等待处理完成）
     */
    void shutdownNow();
    
    /**
     * 获取剩余容量
     * 
     * @return 剩余容量
     */
    long remainingCapacity();
    
    /**
     * 获取运行指标
     * 
     * @return 指标信息
     */
    QueueMetrics getMetrics();
    
    /**
     * 获取当前状态
     * 
     * @return 队列状态
     */
    QueueState getState();
    
    /**
     * 检查是否正在运行
     * 
     * @return true if running
     */
    default boolean isRunning() {
        return getState() == QueueState.RUNNING;
    }
    
    /**
     * 检查是否已关闭
     * 
     * @return true if shutdown
     */
    default boolean isShutdown() {
        return getState() == QueueState.STOPPED;
    }
    
    /**
     * AutoCloseable实现
     */
    @Override
    default void close() {
        try {
            shutdown(Duration.ofSeconds(5));
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            shutdownNow();
        }
    }
} 