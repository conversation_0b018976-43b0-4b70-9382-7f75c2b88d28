package com.simfun.sgf.thread4.examples;

import com.simfun.sgf.thread4.SingleThreadTaskWorker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 兼容性示例 - 与现有SingleThreadDisruptorTaskWorker使用方式完全相同
 * 
 * <AUTHOR>
 */
public class CompatibilityExample {
    
    private static final Logger log = LoggerFactory.getLogger(CompatibilityExample.class);
    
    /**
     * 示例工作器 - 与旧版本写法完全相同
     */
    public static class MyTaskWorker extends SingleThreadTaskWorker<String> {
        
        @Override
        protected void execute(String task, Object... attach) {
            // 处理任务逻辑（与旧版本完全相同）
            log.info("处理任务: {}, 附加参数: {}", task, java.util.Arrays.toString(attach));
            
            // 模拟处理时间
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }
    
    /**
     * 使用示例 - 与旧版本代码完全相同
     */
    public static void demonstrateCompatibility() {
        log.info("=== 兼容性使用示例 ===");
        
        // 创建工作器（与旧版本完全相同）
        MyTaskWorker worker = SingleThreadTaskWorker
            .newBuilder(MyTaskWorker.class)
            .setName("my-worker")
            .setBufferSize(1024)
            .setShutdownWaitTime(5000)
            .build();
        
        try {
            // 启动工作器
            worker.start();
            
            // 添加任务
            worker.addTask("任务1");
            worker.addTask("任务2");
            worker.addTask("任务3");
            
            // 查看任务队列大小
            log.info("当前任务队列大小: {}", worker.getTaskSize());
            
            // 等待任务处理完成
            while (worker.getTaskSize() > 0) {
                Thread.sleep(50);
                log.info("剩余任务: {}", worker.getTaskSize());
            }
            
            log.info("所有任务处理完成");
            
        } catch (Exception e) {
            log.error("处理异常", e);
        } finally {
            // 停止工作器
            worker.stop();
        }
    }
    
    /**
     * 演示try-with-resources用法（新增功能）
     */
    public static void demonstrateModernUsage() {
        log.info("=== 现代化用法示例 ===");
        
        // 使用try-with-resources（新增的便利功能）
        try (var worker = SingleThreadTaskWorker
                .newBuilder(MyTaskWorker.class)
                .setName("modern-worker")
                .setBufferSize(2048)
                .build()) {
            
            worker.start();
            
            // 批量添加任务
            for (int i = 1; i <= 5; i++) {
                worker.addTask("批量任务-" + i);
            }
            
            // 等待处理完成
            while (worker.getTaskSize() > 0) {
                Thread.sleep(100);
            }
            
            // 自动关闭，无需手动调用stop()
        } catch (Exception e) {
            log.error("处理异常", e);
        }
    }
    
    /**
     * 演示配置线程工厂
     */
    public static void demonstrateThreadFactory() {
        log.info("=== 线程工厂配置示例 ===");
        
        try (var worker = SingleThreadTaskWorker
                .newBuilder(MyTaskWorker.class)
                .setName("custom-thread-worker")
                .setThreadFactory(Thread.ofVirtual().name("custom-", 0).factory()) // Java 21虚拟线程
                .build()) {
            
            worker.start();
            worker.addTask("虚拟线程任务");
            
            Thread.sleep(500); // 等待处理
            
        } catch (Exception e) {
            log.error("处理异常", e);
        }
    }
    
    public static void main(String[] args) {
        demonstrateCompatibility();
        demonstrateModernUsage();
        demonstrateThreadFactory();
    }
} 