# meta dir
config.fileDir=webapps/ROOT/meta

geolpdata=webapps/ROOT/data/GeoLite2-Country.mmdb
ip2regiondata=webapps/ROOT/data/ip2region.xdb

## dev:/data/tomcat/webapps/head/
uploadhead=../uploadhead/
uploadkey=uug0VpN1bvPlXpc4lFqE9IT8bQcpR9



# check ignore
# æ¯å¦éªè¯ç­¾å
# å¼åç¯å¢æµè¯åå¼æ ééªç­¾
checkIgnore=false

# google\u652F\u4ED8\u65B0\u9A8C\u8BC1\u65B9\u5F0F
googlePayNewValidator=true

#googleåååè¡¨ name1,name2,name3.....
#ä¸å®è¦ä¸ä¸é¢mapä¸ä¸å¯¹åºï¼å¦åä¼æé®é¢
googlePackageList=com.lc.lssaw.gp,com.lc.lssaw.gpvn

#googleååå¯¹åºè´¦å·IDæ å°
googleAccountMap={'com.lc.lssaw.gp':'<EMAIL>','com.lc.lssaw.gpvn':'<EMAIL>'}
#googleååå¯¹åºP12æä»¶
googleP12Map={'com.lc.lssaw.gp':'../data/api-6711335536439627375-271677-980d320ccf45.p12','com.lc.lssaw.gpvn':'../data/pc-api-7763919483800650018-824-a836079a63a9.p12'}

#è¿ä¸ªç¨ä¸ä¸
# apple.store.type
#appleStoreType=sandbox

#appleåååè¡¨ name1,name2,name3.....
#ä¸å®è¦ä¸ä¸é¢mapä¸ä¸å¯¹åºï¼å¦åä¼æé®é¢
applePackageList=com.lc.lssaw.apple,com.lc.ls.ios.jkt,com.lc.lssaw.applevn

#appleæ¯ä»åæ°Map<packageName, subKey>
appleSubKeyMap={'com.lc.lssaw.apple':'e7d5fca902e142519e376ca261c56dab','com.lc.lssaw.ios.jkt':'47db41ff7a7d4466b271e1b05e731ded','com.lc.lssaw.applevn':'1427c8bafd5443b89bf669c598f23444'}

# notify.config
notifyReceiver=

devtag=true

isForCommandPlayer=false

autoInc=1



#\u9000\u6B3E\u67E5\u8BE2
refundOrderTest=true

#bi\u6253\u70B9\u53C2\u6570
biUrl=
biProd=118000
biTest=true



