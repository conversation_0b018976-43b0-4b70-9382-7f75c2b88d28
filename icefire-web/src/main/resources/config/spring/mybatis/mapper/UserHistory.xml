<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lc.billion.icefire.web.mapper.UserHistoryMapper">
	<resultMap id="userhistory" type="com.lc.billion.icefire.web.bus.user.entity.UserHistory">
		<result property="deviceid" column="deviceid"/>
		<result property="serverId" column="server_id"/>
	</resultMap>
	
	<select id="selectUserHistoryById" resultMap="userhistory" parameterType="String">
		select
		deviceid
		,server_id
		from userhistory
		where deviceid = #{id}
	</select>

	<insert id="insertUserHistory" parameterType="com.lc.billion.icefire.web.bus.user.entity.UserHistory" keyColumn="deviceid">
		insert into userhistory(
			deviceid
			,server_id
		) values(
			#{deviceid}
			,#{serverId}
		) ON DUPLICATE KEY UPDATE
   		 	server_id=#{serverId}
    </insert>

</mapper>
	