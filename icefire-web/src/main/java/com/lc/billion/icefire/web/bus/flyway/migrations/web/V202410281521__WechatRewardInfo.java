package com.lc.billion.icefire.web.bus.flyway.migrations.web;

import com.lc.billion.icefire.web.bus.flyway.AbstractBaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.sql.Statement;

public class V202410281521__WechatRewardInfo extends AbstractBaseJavaMigration {
    private static final String CREATE_TABLE_SQL_WECHAT_REWARD_INFO = "CREATE TABLE `wechat_reward_info` (\n" +
            "  `id` bigint NOT NULL AUTO_INCREMENT,\n" +
            "  `type_id` int NOT NULL,\n" +
            "  `account_id` varchar(128) CHARACTER SET utf8 NOT NULL,\n" +
            "  `server_id` int NOT NULL,\n" +
            "  `role_id` bigint NOT NULL,\n" +
            "  `reward_status` tinyint(1) NOT NULL,\n" +
            "  `create_time` bigint NOT NULL,\n" +
            "  `update_time` bigint NOT NULL,\n" +
            "  PRIMARY KEY (`id`),\n" +
            "  UNIQUE KEY `wechat_reward_info` (`type_id`,`account_id`)\n" +
            ") ENGINE=InnoDB DEFAULT CHARSET=utf8;";

    @Override
    protected void update(Context context) throws Exception {
        var connection = context.getConnection();
        Statement state = connection.createStatement();
        state.addBatch(CREATE_TABLE_SQL_WECHAT_REWARD_INFO);
        state.executeBatch();
        state.clearBatch();
        state.close();
    }
}
