package com.lc.billion.icefire.web.spring;


import jakarta.servlet.ServletContextEvent;
import jakarta.servlet.ServletContextListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.ContextLoaderListener;


public class ContextLoaderListenerWithLogToApp implements ServletContextListener {

	private Logger LOG = LoggerFactory.getLogger(getClass());

	private ContextLoaderListener contextLoaderListener = null;

	public ContextLoaderListenerWithLogToApp() {
		super();
		this.contextLoaderListener = new ContextLoaderListener();
	}

	@Override
	public void contextInitialized(ServletContextEvent sce) {
		try {
			LOG.info("Spring context init");
			this.contextLoaderListener.contextInitialized(sce);
		} catch (RuntimeException re) {
			LOG.error(this.getClass().getSimpleName() + " 初始化报错！", re);
			throw re;
		}
	}

	@Override
	public void contextDestroyed(ServletContextEvent sce) {
		try {
			LOG.info("Spring context destroy");
			this.contextLoaderListener.contextDestroyed(sce);
		} catch (RuntimeException re) {
			LOG.error(this.getClass().getSimpleName() + " 销毁报错！", re);
			throw re;
		}
	}

}
