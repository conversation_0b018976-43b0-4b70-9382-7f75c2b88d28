package com.lc.billion.icefire.web.bus.v2;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.web.bus.gm.service.IRoleEndpointService;
import com.lc.billion.icefire.web.bus.user.entity.User;
import com.lc.billion.icefire.web.bus.user.service.IUserService;
import com.lc.billion.icefire.web.response.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;


@Controller
@RequestMapping(value = "/console/v2/alliance")
public class v2AllianceController {

    @Autowired
    private IUserService userService;

    @Autowired
    private IRoleEndpointService roleService;


    /**
     * GM修改联盟名称
     */
    @RequestMapping(value = "/changeNameByRoleId")
    public @ResponseBody String changeAllianceName(long roleId, long serverId, String newAllianceName) {
        User user = userService.findByRoleId(roleId);
        if (user == null) {
            JSONObject jo = new JSONObject();
            jo.put("ret", -1);
            jo.put("msg", "user is null");
            return jo.toJSONString();
        }

        String retJsonStr =  roleService.changeAllianceName(serverId, roleId, newAllianceName);
        JSONObject jsonObject = JSON.parseObject(retJsonStr);
        if (jsonObject.getInteger("ret") == 1) {
            return JSON.toJSONString(Response.success());
        }
        return JSON.toJSONString(Response.error(-1,"修改联盟名称失败" + jsonObject.getString("msg")));
    }

    /**
     * GM修改联盟简称
     */
    @RequestMapping(value = "/changeAllianceAliasName")
    public @ResponseBody String changeAllianceAliasName(long roleId, long serverId, String newAllianceAliasName) {
        User user = userService.findByRoleId(roleId);
        if (user == null) {
            JSONObject jo = new JSONObject();
            jo.put("ret", -1);
            jo.put("msg", "user is null");
            return jo.toJSONString();
        }

        String retJsonStr =  roleService.changeAllianceAliasName(serverId, roleId, newAllianceAliasName);
        JSONObject jsonObject = JSON.parseObject(retJsonStr);
        if (jsonObject.getInteger("ret") == 1) {
            return JSON.toJSONString(Response.success());
        }
        return JSON.toJSONString(Response.error(-1,"修改联盟简称失败" +  jsonObject.getString("msg")));
    }

    /**
     * GM重置联盟阶级名称
     */
    @RequestMapping(value = "/resetAllianceRankName")
    public @ResponseBody String resetAllianceRankName(long roleId, long serverId, int rankLvl, String newAllianceRankName) {
        User user = userService.findByRoleId(roleId);
        if (user == null) {
            JSONObject jo = new JSONObject();
            jo.put("ret", -1);
            jo.put("msg", "user is null");
            return jo.toJSONString();
        }

        String retJsonStr = roleService.resetAllianceRankName(serverId, roleId, rankLvl, newAllianceRankName);
        JSONObject jsonObject = JSON.parseObject(retJsonStr);
        if (jsonObject.getInteger("ret") == 1) {
            return JSON.toJSONString(Response.success());
        }
        return JSON.toJSONString(Response.error(-1, "修改联盟阶级名称失败" + jsonObject.getString("msg")));
    }

    /**
     * GM重置联盟公告
     */
    @RequestMapping(value = "/resetAllianceNotice")
    public @ResponseBody String resetAllianceNotice(long serverId, Long roleId, Long allianceId) {
        String retJsonStr = roleService.resetAllianceNotice(serverId, roleId, allianceId);
        JSONObject jsonObject = JSON.parseObject(retJsonStr);
        if (jsonObject.getInteger("ret") == 1) {
            return JSON.toJSONString(Response.success());
        }
        return JSON.toJSONString(Response.error(-1,"重置联盟公告失败" +  jsonObject.getString("msg")));
    }

    /**
     * GM重置联盟宣言
     */
    @RequestMapping(value = "/resetAllianceAnnouncement")
    public @ResponseBody String resetAllianceAnnouncement(long serverId, Long allianceId) {
        String retJsonStr = roleService.resetAllianceAnnouncement(serverId, allianceId);
        JSONObject jsonObject = JSON.parseObject(retJsonStr);
        if (jsonObject.getInteger("ret") == 1) {
            return JSON.toJSONString(Response.success());
        }
        return JSON.toJSONString(Response.error(-1,"重置联盟宣言失败" +  jsonObject.getString("msg")));
    }

    /**
     * GM联盟列表
     */
    @RequestMapping(value = "/getPageAllianceList")
    public @ResponseBody String getPageAllianceList(int serverId, int page, int pageSize) {
        String retJsonStr = roleService.getPageAllianceList(serverId, page, pageSize);
        JSONObject jsonObject = JSON.parseObject(retJsonStr);
        if (jsonObject.getInteger("ret") == 0) {
            return retJsonStr;
        }
        return JSON.toJSONString(Response.error(-1,"获取联盟列表失败" +  jsonObject.getString("msg")));
    }
}
