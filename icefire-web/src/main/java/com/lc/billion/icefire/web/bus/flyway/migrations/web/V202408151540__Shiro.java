package com.lc.billion.icefire.web.bus.flyway.migrations.web;

import com.lc.billion.icefire.web.bus.flyway.AbstractBaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.sql.Connection;
import java.sql.PreparedStatement;

/**
 * GM中增加了一个Kvk控制台的页面，增加该页面对应的权限
 * <AUTHOR>
 */
public class V202408151540__<PERSON><PERSON> extends AbstractBaseJavaMigration {

    private static final String INSERT_ACL_SQL = "INSERT INTO `sys_acl` VALUES (?, ?, ?, ?, ?);";
    private static final String[][] INSERT_ACL_SQL_DATA = {
            {"282","console_kvk_start_batch", "/console/kvk/start/batch", "perms[console_kvk]", "kvk-批量合服"},
    };

    private static final String INSERT_ACL_ROLE_SQL = "INSERT INTO `sys_acl_role` VALUES (?, ?);";
    private static final String[][] INSERT_ACL_ROLE_SQL_DATA = {
            { "282", "1" },
    };

    @Override
    protected void update(Context context) throws Exception {
        Connection connection = context.getConnection();
        PreparedStatement insert_acl = connection.prepareStatement(INSERT_ACL_SQL);
        for (String[] acl_data : INSERT_ACL_SQL_DATA) {
            for (int i = 1; i <= acl_data.length; i++) {
                insert_acl.setObject(i, acl_data[i - 1]);
            }
            insert_acl.addBatch();
        }
        insert_acl.executeBatch();
        close(insert_acl);

        PreparedStatement insert_acl_role = connection.prepareStatement(INSERT_ACL_ROLE_SQL);
        for (String[] acl_role_data : INSERT_ACL_ROLE_SQL_DATA) {
            for (int i = 1; i <= acl_role_data.length; i++) {
                insert_acl_role.setObject(i, acl_role_data[i - 1]);
            }
            insert_acl_role.addBatch();
        }
        insert_acl_role.executeBatch();
        close(insert_acl_role);
    }
}

