<%@ page contentType="text/html; charset=UTF-8"%>
<%@taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<div class="panel panel-primary mask">
	<div class="panel-heading">
		<h3 class="panel-title">动态代码</h3>
	</div>
	<div class="panel-body">
		<div class="col-sm-6">
			<div class="panel panel-info">
				<div class="panel-heading">
					<h3 class="panel-title text-center">
						<span class="glyphicon glyphicon-pencil">&nbsp;</span>动态代码
					</h3>
				</div>
				<div class="panel-body">
					<div class="form-group">
						<select id="serverSelect-code" class="selectpicker"
							data-width="100%">
							<c:forEach var="server" items="${servers}">
								<option value="${server.id}">${server.name}</option>
							</c:forEach>
						</select>
					</div>
					<div class="form-group">
						<textarea rows="20" class="form-control" id="dynaCode"
							placeholder="请输入要发送的代码"></textarea>
					</div>
					<div class="form-group">
						<button class="btn btn-info btn-block" type="button"
							id="codeSendBtn">发送</button>
					</div>
				</div>
			</div>
		</div>
		<div class="col-sm-6">
			<div class="panel panel-info">
				<div class="panel-heading">
					<h3 class="panel-title">执行结果</h3>
				</div>
				<div class="panel-body">
					<div>
						<textarea id="codeRunResult" rows="25" class="form-control"
							readonly></textarea>
					</div>
				</div>
			</div>
		</div>

	</div>
</div>