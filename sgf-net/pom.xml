<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
    <parent>
		<groupId>com.simfun.sgf</groupId>
		<artifactId>sgf-parent</artifactId>
		<version>1.2.0-SNAPSHOT</version>
		<relativePath>../sgf-parent</relativePath>
	</parent>

	<artifactId>sgf-net</artifactId>

	<name>Simple Game Framework Net</name>
	<description>Simple Game Framework Net</description>


	<dependencies>
		<dependency>
			<groupId>com.simfun.sgf</groupId>
			<artifactId>sgf-core</artifactId>
		</dependency>
		<dependency>
			<groupId>io.netty</groupId>
			<artifactId>netty-all</artifactId>
			<scope>compile</scope>
			<optional>true</optional>
		</dependency>
 		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>jcl-over-slf4j</artifactId>
		</dependency>
		<dependency>
			<groupId>org.simpleframework</groupId>
			<artifactId>simple-xml</artifactId>
			<scope>compile</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
			<scope>compile</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.reflections</groupId>
			<artifactId>reflections</artifactId>
			<scope>compile</scope>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.apache.thrift</groupId>
			<artifactId>libthrift</artifactId>
			<version>0.16.0</version>
			<scope>compile</scope>
			<optional>true</optional>
		</dependency>
<!-- 		<dependency>
			<groupId>net.jpountz.lz4</groupId>
			<artifactId>lz4</artifactId>
			<scope>compile</scope>
			<optional>true</optional>
		</dependency>
 -->
 		<dependency>
		  <groupId>org.lz4</groupId>
		  <artifactId>lz4-pure-java</artifactId>
		</dependency>		

		<!-- test -->
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.easymock</groupId>
			<artifactId>easymock</artifactId>
			<scope>test</scope>
		</dependency>
 		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-nop</artifactId>
			<scope>test</scope>
		</dependency>
		<!-- <dependency> <groupId>org.slf4j</groupId> <artifactId>slf4j-simple</artifactId> 
			<version>1.6.6</version> </dependency> -->
		<!-- https://mvnrepository.com/artifact/io.fsq/spindle -->
		<!-- <dependency> <groupId>io.fsq</groupId> <artifactId>spindle_2.11</artifactId> 
			<version>1.3.2</version> </dependency> -->		<!-- https://mvnrepository.com/artifact/org.json/json -->
		<!-- <dependency> <groupId>com.esotericsoftware</groupId> <artifactId>reflectasm</artifactId> 
			<version>1.11.9</version> </dependency> -->
		<!-- https://mvnrepository.com/artifact/commons-codec/commons-codec -->
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<scope>provided</scope>
		</dependency>
	</dependencies>

</project>