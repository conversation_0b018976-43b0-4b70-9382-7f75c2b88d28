# Worker 基准测试运行指南

## 测试概述

这个基准测试比较了两种单线程工作器的性能：
- `SingleThreadWorker`: 使用传统的`ExecutorService`实现
- `SingleThreadDisruptorTaskWorker`: 使用高性能的Disruptor无锁队列实现

## JMH 基准测试框架

现在项目集成了JMH (Java Microbenchmark Harness) 框架，这是OpenJDK提供的专业微基准测试工具，能够提供更准确和可靠的性能测试结果。

### JMH 优势
- **预热机制**: 自动处理JVM预热，确保测试结果准确性
- **统计分析**: 提供置信区间、标准差等统计信息
- **多种测试模式**: 支持吞吐量、平均时间、采样时间等多种测试模式
- **防止编译器优化**: 通过Blackhole等机制防止编译器过度优化影响测试结果

## 测试指标

### 1. 吞吐量 (Throughput)
- 每秒处理的消息数量 (messages/second)
- 衡量系统在单位时间内的处理能力

### 2. 延迟 (Latency)
- 从消息创建到处理完成的平均时间 (纳秒)
- 衡量系统的响应速度

### 3. 内存使用 (Memory Usage)
- 测试期间的内存消耗差异 (bytes)
- 衡量内存效率

## 运行测试

### JMH 基准测试（推荐）

#### 1. 简化版JMH测试（IDE中运行）
```java
// 直接在IDE中运行 SimpleWorkerBenchmark.main() 方法
// 或者使用Maven命令：
mvn test-compile exec:java -Dexec.mainClass="com.lc.billion.icefire.game.benchmark.SimpleWorkerBenchmark"
```

#### 2. 完整版JMH测试
```bash
# 编译JMH基准测试
mvn test-compile

# 运行JMH测试
mvn exec:java -Dexec.mainClass="com.lc.billion.icefire.game.benchmark.WorkerJMHBenchmark"

# 或者使用JMH插件
mvn jmh:run
```

#### 3. 自定义JMH参数
```bash
# 运行特定的基准测试方法
mvn exec:java -Dexec.mainClass="org.openjdk.jmh.Main" -Dexec.args="WorkerJMHBenchmark.benchmarkSingleThreadWorker"

# 指定测试参数
mvn exec:java -Dexec.mainClass="org.openjdk.jmh.Main" -Dexec.args="-wi 5 -i 10 -f 1 WorkerJMHBenchmark"
```

### 传统测试（兼容性）

#### 快速验证测试
```bash
mvn test -Dtest=WorkerBenchmarkTest#quickBenchmark
```

#### 完整基准测试
```bash
mvn test -Dtest=WorkerBenchmarkTest#benchmarkComparison
```

#### 压力测试
```bash
mvn test -Dtest=WorkerBenchmarkTest#stressTest
```

## 测试场景

### 1. 基准对比测试
- 测试消息数量: 10,000、50,000、100,000、500,000
- 每个测试前都会进行预热
- 测量吞吐量、延迟和内存使用

### 2. 压力测试
- 1,000,000 条消息
- 4个生产者线程并发发送
- 测试高负载下的性能表现

## IDE 中运行JMH测试

### IDEA 配置
1. 右键点击 `SimpleWorkerBenchmark.java` 或 `WorkerJMHBenchmark.java`
2. 选择 "Run 'SimpleWorkerBenchmark.main()'"
3. 或者创建新的运行配置：
   - Main class: `com.lc.billion.icefire.game.benchmark.SimpleWorkerBenchmark`
   - VM options: `-Xmx2g -XX:+UseG1GC`
   - Working directory: `$MODULE_WORKING_DIR$`

### Eclipse 配置
1. 右键点击基准测试类
2. Run As -> Java Application
3. 在Run Configurations中设置：
   - Main class: `com.lc.billion.icefire.game.benchmark.SimpleWorkerBenchmark`
   - Arguments -> VM arguments: `-Xmx2g -XX:+UseG1GC`

### VS Code 配置
在 `.vscode/launch.json` 中添加：
```json
{
    "type": "java",
    "name": "Worker Benchmark",
    "request": "launch",
    "mainClass": "com.lc.billion.icefire.game.benchmark.SimpleWorkerBenchmark",
    "vmArgs": ["-Xmx2g", "-XX:+UseG1GC"],
    "projectName": "icefire-game"
}
```

## JMH 测试结果解读

### 输出格式说明
```
Benchmark                                           Mode  Cnt    Score    Error  Units
SimpleWorkerBenchmark.benchmarkSingleThreadWorker  thrpt    3  12345.678 ± 123.456  ops/s
SimpleWorkerBenchmark.benchmarkDisruptorTaskWorker thrpt    3  23456.789 ± 234.567  ops/s
```

- **Mode**: 测试模式 (thrpt=吞吐量, avgt=平均时间)
- **Cnt**: 测试迭代次数
- **Score**: 平均测试结果
- **Error**: 误差范围 (±)
- **Units**: 单位 (ops/s=每秒操作数, ns=纳秒)

## 预期结果

### SingleThreadDisruptorTaskWorker 优势
1. **更高的吞吐量**: Disruptor的无锁队列设计通常能提供更高的消息处理吞吐量
2. **更低的延迟**: 避免了传统队列的锁竞争，减少了消息处理延迟
3. **更好的CPU缓存友好性**: Ring Buffer设计对CPU缓存更友好

### SingleThreadWorker 优势
1. **更低的内存使用**: 没有预分配的Ring Buffer，内存使用可能更少
2. **更简单的实现**: 基于标准JDK组件，实现更简单

## 影响因素

### 1. 硬件因素
- CPU核心数和频率
- 内存大小和延迟
- CPU缓存大小

### 2. 软件因素
- JVM版本和配置
- GC策略
- 系统负载

### 3. 测试参数
- 消息大小
- 处理时间
- 队列大小

## 分析建议

1. **关注吞吐量趋势**: 消息数量增加时，哪种实现的性能退化更小
2. **延迟分布**: 不只看平均延迟，还要关注延迟的稳定性
3. **内存效率**: 在高吞吐量场景下的内存使用情况
4. **CPU使用率**: 达到相同吞吐量时的CPU消耗

## 结论指导

根据测试结果选择合适的实现：

- **高吞吐量场景**: 如果需要处理大量消息，优先选择DisruptorTaskWorker
- **低延迟要求**: 如果对响应时间要求严格，DisruptorTaskWorker通常表现更好
- **内存敏感**: 如果内存资源有限，可能需要考虑SingleThreadWorker
- **简单维护**: 如果追求简单性和可维护性，SingleThreadWorker可能更合适

## 优化建议

### 对于 DisruptorTaskWorker
1. 调整Ring Buffer大小 (`bufferSize`)
2. 选择合适的等待策略 (`WaitStrategy`)
3. 考虑CPU亲和性设置

### 对于 SingleThreadWorker
1. 优化任务队列实现
2. 减少锁竞争
3. 考虑批量处理策略

## JMH 最佳实践

### 1. 测试环境准备
- 关闭不必要的后台程序
- 使用稳定的电源供应（笔记本请插电源）
- 确保CPU温度稳定，避免降频
- 关闭动态CPU频率调节

### 2. JVM 参数建议
```bash
-Xmx2g                    # 足够的堆内存
-XX:+ZGC              # 使用ZGC垃圾收集器
-XX:+UnlockExperimentalVMOptions  # 解锁实验性选项
-XX:+UseTransparentHugePages      # 使用透明大页（Linux）
```

### 3. 测试参数调优
- **预热次数**: 至少3-5次，复杂场景可增加到10次
- **测试次数**: 5-10次，获得稳定的统计结果
- **Fork数量**: 通常1-3次，避免JVM间差异
- **线程数**: 根据测试目标选择，单线程测试使用1

### 4. 结果分析技巧
- 关注误差范围，误差过大说明测试不稳定
- 多次运行验证结果一致性
- 使用不同JVM版本对比
- 记录测试环境信息（CPU、内存、JVM版本等）

### 5. 常见陷阱
- **死代码消除**: 使用Blackhole.consume()防止编译器优化
- **循环展开**: 避免简单循环被过度优化
- **GC影响**: 选择合适的GC策略，避免测试期间GC干扰
- **系统负载**: 确保测试期间系统负载稳定 