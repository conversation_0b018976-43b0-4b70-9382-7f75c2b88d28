package com.lc.billion.icefire.game.biz.service.impl.bilog;

/**
 * @ClassName GaLogType
 * @Description
 * <AUTHOR>
 * @Date 2024/4/24 11:31
 * @Version 1.0
 */
public enum GaLogType {

    //track默认类型，所有参数需要手动传参，其它类型会默认自带部分参数
    TRACK("track"),
    //coin表示金流相关事件
    COIN("coin"),
    //pay表示支付相关事件
    PAY("pay"),
    //game表示游戏行为
    GAME("game"),
    //login表示登录注册相关事件
    LOGIN("login"),
    //push表示推送相关行为
    PUSH("push"),
    // profile用户属性
    PROFILE("profile"),
    // 审计资产
    FINANCIAL("asset"),
    ;


    private String val;

    GaLogType(String type) {
        val = type;
    }

    public String getVal() {
        return val;
    }
}
