package com.lc.billion.icefire.game.msg.handler.impl.alliance.update;

import com.lc.billion.icefire.game.biz.service.impl.CheckStringServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.chatsdk.ChatSDKService;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.protocol.GcPlayerInvalidChar;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceServiceImpl;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgAllianceUpdateFlag;

/**
 *
 * <AUTHOR>
 * @sine 2015年8月10日 下午5:04:27
 *
 */
@Controller
public class CgAllianceUpdateFlagHandler extends CgAbstractMessageHandler<CgAllianceUpdateFlag> {
	@Autowired
	private CheckStringServiceImpl checkStringService;
	@Autowired
	private ChatSDKService chatSDKService;
	@Autowired
	private AllianceServiceImpl allianceService;

	@Override
	public void handle(Role role, CgAllianceUpdateFlag message) {
		// banner 是旗帜图形，值如：ui/images/alliance/alliance_Flag_03
		// badge 是徽章，是旗帜上的字，需要检测
		String badge = message.getBadge();
		if (!checkStringService.checkName(badge, 1, false)) {  // 页面文案写的只能1个字
			ErrorLogUtil.errorLog("update flag failed. invalid badge", "player",role.getId(), "invalid string", badge);
			role.send(new GcPlayerInvalidChar(badge));
			return;
		}

		chatSDKService.checkWord(role, badge, false, (invalid, text) -> {
			if (invalid || !badge.equals(text)) {
				role.send(new GcPlayerInvalidChar(badge));
			} else {
				allianceService.updateBannerOrBadge(role, message.getBanner(), message.getBannerColor(), badge, message.getBadgeColor());
			}
		});
	}

}
