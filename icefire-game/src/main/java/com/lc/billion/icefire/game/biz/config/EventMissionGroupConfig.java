package com.lc.billion.icefire.game.biz.config;

import com.fasterxml.jackson.databind.JsonNode;
import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.annotation.MetaMap;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import com.lc.billion.icefire.core.utils.SplitStringUtil;
import com.simfun.sgf.utils.JavaUtils;
import org.locationtech.jts.util.StringUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 事件活动与具体任务的对应关系表
 *
 * <AUTHOR>
 */
@Config(name = "EventMissionGroup", metaClass = EventMissionGroupConfig.EventMissionGroupMeta.class)
public class EventMissionGroupConfig {

    @MetaMap
    private Map<String, EventMissionGroupMeta> idMap;

    public EventMissionGroupMeta getMeta(final String id) {
        return idMap.get(id);
    }

    private Map<String, Map<String, Integer>> groupMissionStepMap = new HashMap<>();

    public void init(List<EventMissionGroupMeta> list) {
        for (EventMissionGroupMeta meta : list) {
            Map<String, Integer> missionStepMap = this.groupMissionStepMap.computeIfAbsent(meta.groupid, k -> new HashMap<>());
            Arrays.stream(StringUtil.split(meta.missionGroup, SplitStringUtil.SPLIT_VERTICAL))
                    .flatMap(missionIds -> Arrays.stream(StringUtil.split(missionIds, SplitStringUtil.SPLIT_SEMICOLON)))
                    .forEach(missionId -> missionStepMap.put(missionId, meta.step));
        }
    }

    public int getMissionStep(String groupid, String missionid) {
        Map<String, Integer> stepMap = groupMissionStepMap.get(groupid);
        if (stepMap != null) {
            return stepMap.get(missionid);
        }
        return -1;
    }

    public Set<String> getMissionIds(String groupId) {
        Map<String, Integer> map = groupMissionStepMap.get(groupId);
        if (map == null) {
            return Collections.emptySet();
        }
        return map.keySet();
    }




    public static class EventMissionGroupMeta extends AbstractMeta {


        protected String groupid;

        protected int step;

        protected String missionGroup;

        protected Set<String> missionIdSet = new HashSet<>();

        public String getGroupid() {
            return groupid;
        }

        public void setGroupid(String groupid) {
            this.groupid = groupid;
        }

        public int getStep() {
            return step;
        }

        public void setStep(int step) {
            this.step = step;
        }

        public String getMissionGroup() {
            return missionGroup;
        }

        public void setMissionGroup(String missionGroup) {
            this.missionGroup = missionGroup;
        }

        public Set<String> getMissionIdSet() {
            return missionIdSet;
        }

        public void setMissionIdSet(Set<String> missionIdSet) {
            this.missionIdSet = missionIdSet;
        }

        @Override
        public void init(JsonNode json) {
            super.init(json);

            if (JavaUtils.bool(missionGroup)) {
                String[] missionIdArray = StringUtil.split(missionGroup, SplitStringUtil.SPLIT_SEMICOLON);
                if (missionIdArray != null) {
                    this.missionIdSet = Arrays.stream(missionIdArray).collect(Collectors.toSet());
                }
            }
        }
    }
}
