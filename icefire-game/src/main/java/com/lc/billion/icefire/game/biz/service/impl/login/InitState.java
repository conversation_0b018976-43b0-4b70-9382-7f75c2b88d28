package com.lc.billion.icefire.game.biz.service.impl.login;

import org.springframework.stereotype.Service;

import com.lc.billion.icefire.game.biz.model.player.Player;
import com.lc.billion.icefire.game.biz.service.AbstractOnlineState;

/**
 * 初始状态
 * <p>
 * Player实例化后，默认就应该是这个状态。
 *
 * <AUTHOR>
 */
@Service
public class InitState extends AbstractOnlineState {

	@Override
	public Type getType() {
		return Type.INIT;
	}

	@Override
	public void connect(Player player) {
		changeState(player, Type.CONNECTED);
	}

}
