package com.lc.billion.icefire.game.msg.handler.impl.official;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.officials.OfficialsService;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgOfficialHistory;
import com.lc.billion.icefire.protocol.CgOfficialRewardsInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;


@Controller
public class CgOfficialHistoryHandler extends CgAbstractMessageHandler<CgOfficialHistory> {

    @Autowired
    private OfficialsService officialsService;

    @Override
    protected void handle(Role role, CgOfficialHistory message) {
        officialsService.getOfficialHistory(role, message.getType(), message.getOfficialMetaId(), message.getSeason());
    }
}