package com.lc.billion.icefire.game.biz.service.impl.libao.function;

import com.lc.billion.icefire.game.biz.model.libao.LiBaoFunction;
import com.lc.billion.icefire.game.biz.model.libao.LiBaoFunctionType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.expedition.ExpeditionServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Pve通过关卡等级
 */
@Service
public class ExpeditionLevelFunction implements LiBaoConditionCheck{

    @Autowired
    private ExpeditionServiceImpl expeditionService;

    @Override
    public LiBaoFunctionType getType() {
        return LiBaoFunctionType.EXPEDITION_LEVEL;
    }

    @Override
    public boolean check(Role role, LiBaoFunction func) {
        return onCheck(role,func);
    }

    @Override
    public boolean onCheck(Role role, LiBaoFunction func, Object... input) {
        var level = func.getCp().getInt(0);
        return expeditionService.getExpeditionLevel(role) >= level;
    }
}
