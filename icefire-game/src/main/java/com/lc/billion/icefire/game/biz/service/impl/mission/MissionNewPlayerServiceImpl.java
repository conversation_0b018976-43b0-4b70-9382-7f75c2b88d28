package com.lc.billion.icefire.game.biz.service.impl.mission;

import com.google.common.collect.Lists;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.biz.config.ActivityCrazyMissionRewardConfig;
import com.lc.billion.icefire.game.biz.config.ActivityListConfig;
import com.lc.billion.icefire.game.biz.config.CrazyMissionConfig;
import com.lc.billion.icefire.game.biz.config.MissionConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.NewPlayerMissionDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.ActivityDao;
import com.lc.billion.icefire.game.biz.manager.MissionManager;
import com.lc.billion.icefire.game.biz.manager.RoleManager;
import com.lc.billion.icefire.game.biz.model.activity.Activity;
import com.lc.billion.icefire.game.biz.model.activity.ActivityType;
import com.lc.billion.icefire.game.biz.model.email.EmailConstants;
import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.game.biz.model.mission.MissionType;
import com.lc.billion.icefire.game.biz.model.mission.newplayer.NewPlayerMission;
import com.lc.billion.icefire.game.biz.model.mission.newplayer.NewPlayerMissionItem;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.activity.handler.CrownedKingActivityHandler;
import com.lc.billion.icefire.game.biz.service.impl.bilog.BiLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.email.MailService;
import com.lc.billion.icefire.game.biz.service.impl.functionSwitch.FunctionSwitchService;
import com.lc.billion.icefire.game.biz.service.impl.functionSwitch.FunctionType;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.mission.event.MissionEvent;
import com.lc.billion.icefire.game.biz.service.impl.vip.VipServiceImpl;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.game.support.LogReasons;
import com.lc.billion.icefire.protocol.*;
import com.lc.billion.icefire.protocol.constant.PsErrorCode;
import com.lc.billion.icefire.protocol.constant.PsRewardClaimStatus;
import com.lc.billion.icefire.protocol.constant.UnlockModule;
import com.lc.billion.icefire.protocol.structure.PsSimpleItem;
import com.simfun.sgf.utils.TimeUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * 新手任务 对应新手7日活动中的任务，原low中的王者之路
 */
@Service
public class MissionNewPlayerServiceImpl {

    public static final Logger logger = LoggerFactory.getLogger(MissionNewPlayerServiceImpl.class);

    @Autowired
    private ConfigServiceImpl configService;
    @Autowired
    private ServiceDependency srvDpd;
    @Autowired
    private MissionManager missionManager;
    @Autowired
    private NewPlayerMissionDao newPlayerMissionDao;
    @Autowired
    private RoleManager roleManager;
    @Autowired
    private ActivityDao activityDao;
    @Autowired
    private CrownedKingActivityHandler crownedKingActivityHandler;
    @Autowired
    private MailService mailService;
    @Autowired
    private BiLogUtil biLogUtil;
    @Autowired
    private VipServiceImpl vipService;
    @Autowired
    private FunctionSwitchService functionSwitchService;

    @PostConstruct
    public void init() {

    }

    public void claimAllReward(List<SimpleItem> itemList, List<PsRewardClaimStatus> rewardList, boolean vip) {
        if (rewardList == null) {
            return;
        }

        var config = configService.getConfig(ActivityCrazyMissionRewardConfig.class);
        for (var index = 1; index < rewardList.size(); index++) {
            if (rewardList.get(index - 1) != PsRewardClaimStatus.CAN_BE_CLAIMED) {
                continue;
            }
            rewardList.set(index - 1, PsRewardClaimStatus.ALREADY_BE_CLAIMED);

            var mate = config.getMetaMap().get(String.valueOf(index));
            if (mate == null) {
                ErrorLogUtil.errorLog("config not found", "index",index);
                continue;
            }

            var reward = vip ? mate.getVipReward() : mate.getReward();
            // 发奖
            if (StringUtils.isNotBlank(reward)) {
                List<SimpleItem> drop = srvDpd.getDropService().drop(reward);
                itemList.addAll(drop);
            }
        }
    }

    /**
     * <AUTHOR>
     * @Date 2024-04-18 17:10:52.191
     * @Param [role]
     * @Return void
     * @Description 刷新任务 主要是点数
     */
    public void refreshMission(Role role) {
        var level = vipService.getVipLevel(role);
        NewPlayerMission newPlayerMission = missionManager.getNewPlayerMission(role.getPersistKey());
        if (newPlayerMission == null) {
            return;
        }

        newPlayerMission.refreshPoint(level);
    }

    public void onVipLevelUp(Role role) {
        refreshMission(role);
        // 发送任务列表
        sendNewPlayerMissionMsg(role);
    }

    /**
     * 玩家登陆时，如果有未领取的奖励，邮件发奖
     *
     * @param role
     */
    public void sendUnreceivedRewardMail(Role role) {

        NewPlayerMission newPlayerMission = missionManager.getNewPlayerMission(role.getPersistKey());
        if (newPlayerMission == null) {
            return;
        }

        if (!newPlayerMission.isOverlap()) {
            return;
        }

        List<SimpleItem> reissueItems = new ArrayList<>();

        // 先领普通奖励 刷新分数
        var day = newPlayerMission.getUnlockDay();
        for (int i = 1; i <= day; i++) {
            var missions = newPlayerMission.getMissions().get(i);
            if (null == missions) {
                continue;
            }
            for (var mission : missions.values()) {
                if (!mission.isFinish() || mission.isAward()) {
                    continue;
                }
                var missionId = mission.getMissionId();
                NewPlayerMissionItem mItem = newPlayerMission.getMissionItem(missionId);
                if (mItem == null) {
                    logger.info("getRewards MissionItem is null !!  missionId:{}", missionId);
                    continue;
                }
                if (!mItem.isFinish()) {
                    logger.info("getRewards MissionItem is not finish !! missionId:{}", missionId);
                    continue;
                }
                if (mItem.isAward()) {
                    logger.info("getRewards MissionItem is Award !! missionId:{}", missionId);
                    continue;
                }
                MissionConfig.MissionMeta meta = mItem.getMeta();
                if (meta == null) {
                    logger.info("getRewards MissionMeta is null !! missionId:{}", missionId);
                    continue;
                }
                mItem.setAward(true);

                reissueItems.addAll(srvDpd.getDropService().drop(meta.getReward()));
            }
        }

        // 刷新额外奖励
        refreshMission(role);

        // 再领宝箱奖励
        claimAllReward(reissueItems, newPlayerMission.getCommonRewards(), false);
        claimAllReward(reissueItems, newPlayerMission.getVipRewards(), true);

        newPlayerMissionDao.save(newPlayerMission);

        if (!reissueItems.isEmpty()) {
            // 发送奖励邮件
            List<String> params = new ArrayList<>();
            mailService.sendSystemEmail(role, EmailConstants.NEW_PLAYER_MISSION_REWARD_MAIL, reissueItems, params);
            missionManager.saveNewPlayerMission(newPlayerMission);
        }

    }

    public void onEnterWorld(Role role) {
        if (!functionSwitchService.isOpen(FunctionType.NOVICE.getId(), role)) {
            return;
        }
        NewPlayerMission newPlayerMission = this.missionManager.getNewPlayerMission(role.getId());
        if (newPlayerMission == null) {
            return;
        }

        // 关闭任务判断
        if (newPlayerMission.isOpen() && newPlayerMission.isOverlap()) {
            // 关闭新手任务，并发送奖励
            endNewPlayerMission(role);
            // 奖励补发检测
            sendUnreceivedRewardMail(role);
            return;
        }

        // 解锁天数
        newPlayerMission.unlockDay();

        // 发送任务列表
        sendNewPlayerMissionMsg(role);
    }

    /**
     * <AUTHOR>
     * @Date 2024-03-18 17:37:08.693
     * @Param [role, unlockModule]
     * @Return void
     * @Description 玩家某个模块解锁
     */
    public void onModuleUnlock(Role role, UnlockModule unlockModule) {
        if (unlockModule.equals(UnlockModule.NEW_PLAYER_MISSION)) {
            biLogUtil.unlockNewPlayerMission(role);
            openNewPlayerMission(role);
        }
    }

    /**
     * 0点重置
     *
     * @param role
     */
    public void onResetDayData(Role role) {
        if (!functionSwitchService.isOpen(FunctionType.NOVICE.getId(), role)) {
            return;
        }
        NewPlayerMission newPlayerMission = this.missionManager.getNewPlayerMission(role.getPersistKey());
        if (newPlayerMission == null || !newPlayerMission.isOpen()) {// 新手任务已经关闭了，不用管了
            return;
        }

        //关闭任务
        if (newPlayerMission.isOpen() && newPlayerMission.isOverlap()) {
            // 关闭新手任务，并发送奖励
            endNewPlayerMission(role);
            // 奖励补发检测
            sendUnreceivedRewardMail(role);
            return;
        }

        // 解锁天数
        newPlayerMission.unlockDay();

        if (!role.isOnline()) {
            return;
        }

        onMissionFinish(role, MissionType.ONLINE_DAYS, role);
    }

    // 获得新手冲级任务
    public void sendNewPlayerMissionMsg(Role role) {
        NewPlayerMission newPlayerMission = this.missionManager.getNewPlayerMission(role.getId());
        if (newPlayerMission == null) {
            return;
        }
        // 开放且未过期
        if (newPlayerMission.isOpen() && !newPlayerMission.isOverlap()) {
            // 发送新手任务消息
            var msg = toMissionListMsg(newPlayerMission);
            role.send(msg);
        }
    }

    /**
     * 获取完成的任务数
     *
     * @param role
     * @return
     */
    public int getFinishNum(Role role) {
        NewPlayerMission newPlayerMission = missionManager.getNewPlayerMission(role.getPersistKey());
        int finishNum = 0;
        for (NewPlayerMissionItem item : newPlayerMission.getAll()) {
            if (item.isFinish()) {
                finishNum++;
            }
        }
        return finishNum;
    }

    /**
     * 活动到时结束
     *
     * @param role
     */
    private void endNewPlayerMission(Role role) {
        NewPlayerMission newPlayerMission = missionManager.getNewPlayerMission(role.getPersistKey());

        // 结束任务
        newPlayerMission.end();
        missionManager.saveNewPlayerMission(newPlayerMission);
        if (role.isOnline()) {
            // 发送新手任务消息
            Activity activity = activityDao.findActivityByActivityType(ActivityType.CROWNED_KING);
            if (activity == null) {
                // 新手付费7日活动null
                return;
            }
            // 活动元数据
            var activityListMeta = configService.getConfig(ActivityListConfig.class).getMetaById(activity.getMetaId());
            if (activityListMeta == null) {
                ErrorLogUtil.errorLog("新手七日任务活动meta为空","activityMetaId",activity.getMetaId());
                throw new AlertException("新手七日任务活动meta为空","activityMetaId",activity.getMetaId());
            }
            var roleActivityInfo = crownedKingActivityHandler.getRoleActivityInfo(role, activityListMeta);
            var msg = new GcAcitivityUpdate(roleActivityInfo);
            role.send(msg);
        }
    }

    public void onMissionFinish(Role role, MissionType missionType, Object... params) {
        ///////// 新的任务完成///////////////
        // 加上try 防止打点时出错，影响其他逻辑
        if (role == null) {
            ErrorLogUtil.errorLog("onMissionFinish role is null");
            return;
        }
        try {
            NewPlayerMission newPlayerMission = missionManager.getNewPlayerMission(role.getPersistKey());
            if (newPlayerMission == null) {
                return;
            }

            List<NewPlayerMissionItem> needUpdateMissions = new ArrayList<>();
            Collection<NewPlayerMissionItem> allMissions = newPlayerMission.getAll();
            for (NewPlayerMissionItem item : allMissions) {
                if (!item.isFinish()) {
                    if (onNewPlayerMissionFinish(role, missionType, item, params)) {
                        needUpdateMissions.add(item);
                    }
                }
            }
            missionManager.saveNewPlayerMission(newPlayerMission);
            // 发送更新
            if (role.isOnline() && !needUpdateMissions.isEmpty()) {
                role.send(toMissionUpdateMsg(needUpdateMissions, newPlayerMission));
            }

        }catch (ExpectedException ignored) {

        } catch (Exception e) {
            ErrorLogUtil.exceptionLog("任务完成异常", e,"roleId",role.getPersistKey());
        }

    }

    /**
     * 是否需要更新任务数据
     *
     * @param role
     * @param type
     * @param mission
     * @param params
     * @return
     */
    private boolean onNewPlayerMissionFinish(Role role, MissionType type, NewPlayerMissionItem mission, Object[] params) {
        MissionConfig.MissionMeta meta = configService.getConfig(CrazyMissionConfig.class).getById(mission.getMissionId());
        if (meta == null) {
            return false;
        }

        // 任务类型不匹配
        if (type != meta.getType()) {
            return false;
        }

        MissionEvent event = meta.getType().getEvent();
        if (event == null) {
            ErrorLogUtil.errorLog("not find mission event impl", "missionId",mission.getMissionId(), "type",type);
            return false;
        }

        // 条件未达成
        if (!event.check(mission, meta, params)) {
            return false;
        }

        boolean effect = event.effect(mission, meta, params);
        if (!effect) {// 对任务没有造成影响
            return false;
        }
        if (mission.getStatus() == MissionConstants.MISSION_STATUS_FINISH) {
            roleManager.onMissionChangeState(role, meta.getId(), meta.getMissionGroup(), (byte) 1, meta.getMissionType(), "activity:" + "NewMission");
        }
        NewPlayerMission newPlayerMission = missionManager.getNewPlayerMission(role.getPersistKey());
        if (null == newPlayerMission) {
            return false;
        }
        missionManager.saveNewPlayerMission(newPlayerMission);
        // 发送更新
        if (role.isOnline()) {
            role.send(toMissionUpdateMsg(Collections.singletonList(mission), newPlayerMission));
            if (mission.getStatus() == MissionConstants.MISSION_STATUS_FINISH) {
                crownedKingActivityHandler.receiveReward(role, "", "");
            }
        }
        return true;

    }

    public void gmSetDay(Role role, int day) {
        NewPlayerMission newPlayerMission = missionManager.getNewPlayerMission(role.getPersistKey());
        if (newPlayerMission == null) {
            return;
        }

        var now = TimeUtil.getNow();
        var startTime = now - (TimeUtil.DAY_MILLIS * (day - 1));
        var endTime = TimeUtils.getBeginOfDay(startTime) + TimeUtil.DAY_MILLIS * 7;
        newPlayerMission.setOpen(true);
        newPlayerMission.setMissionOpenTime(startTime);
        newPlayerMission.setEndTime(endTime);
        newPlayerMission.unlockDay();
        newPlayerMissionDao.save(newPlayerMission);

        sendNewPlayerMissionMsg(role);
    }

    public void claimMissionRewards(Role role, String missionId) {
        if (!functionSwitchService.isOpen(FunctionType.NOVICE.getId(), role)) {
            return;
        }
        PsErrorCode result = PsErrorCode.ERROR;
        List<SimpleItem> rewardList = new ArrayList<>();
        try {
            NewPlayerMission newPlayerMission = missionManager.getNewPlayerMission(role.getId());
            if (newPlayerMission == null) {
                return;
            }
            NewPlayerMissionItem mItem = newPlayerMission.getMissionItem(missionId);
            if (mItem == null) {
                logger.info("getRewards MissionItem is null !!  missionId:{}", missionId);
                return;
            }
            if (!mItem.isFinish()) {
                logger.info("getRewards MissionItem is not finish !! missionId:{}", missionId);
                return;
            }
            if (mItem.isAward()) {
                logger.info("getRewards MissionItem is Award !! missionId:{}", missionId);
                return;
            }
            MissionConfig.MissionMeta meta = mItem.getMeta();
            if (meta == null) {
                logger.info("getRewards MissionMeta is null !! missionId:{}", missionId);
                return;
            }
            mItem.setAward(true);
            result = PsErrorCode.SUCCESS;
            // 刷新额外奖励
            refreshMission(role);
            ArrayList<NewPlayerMissionItem> missions = Lists.newArrayList();
            missions.add(mItem);
            role.send(toMissionUpdateMsg(missions, newPlayerMission));

            rewardList = srvDpd.getDropService().drop(meta.getReward());
            srvDpd.getItemService().give(role, rewardList, LogReasons.ItemLogReason.NEW_PLAYER_MISSION_REWARD);

            // ga 打点
            biLogUtil.newPlayerMissionReward(role, missionId, rewardList);

            roleManager.onMissionChangeState(role, meta.getId(), meta.getMissionGroup(), (byte) 2, meta.getMissionType(), "activity:" + "NewMission");
            missionManager.saveNewPlayerMission(newPlayerMission);
        } finally {
            role.send(toMissionRewardMsg(missionId, result, rewardList));
            crownedKingActivityHandler.receiveReward(role, "", "");
        }
    }

    public void claimMissionBoxReward(Role role, int mateId, boolean vip) {
        if (!functionSwitchService.isOpen(FunctionType.NOVICE.getId(), role)) {
            return;
        }
        NewPlayerMission newPlayerMission = missionManager.getNewPlayerMission(role.getPersistKey());
        if (newPlayerMission == null) {
            return;
        }

        var rewardList = new ArrayList<PsSimpleItem>();
        var config = configService.getConfig(ActivityCrazyMissionRewardConfig.class);
        var index = mateId - 1;
        var mate = config.getMetaMap().get(String.valueOf(mateId));
        if (mate == null) {
            ErrorLogUtil.errorLog("config not found", "index",index);
            var msg = toMissionBoxRewardMsg(mateId, vip, PsErrorCode.ERROR, new ArrayList<>());
            role.send(msg);
            return;
        }
        if (vip) {
            var record = newPlayerMission.getVipRewards();
            if (record.get(index) != PsRewardClaimStatus.CAN_BE_CLAIMED) {
                var msg = toMissionBoxRewardMsg(mateId, vip, PsErrorCode.ERROR, new ArrayList<>());
                role.send(msg);
                return;
            }

            var reward = mate.getVipReward();
            // 发奖
            if (StringUtils.isNotBlank(reward)) {
                List<SimpleItem> drop = srvDpd.getDropService().drop(reward);
                drop.forEach(simpleItem -> {
                    rewardList.add(simpleItem.toPsObject());
                });
                srvDpd.getItemService().give(role, drop, LogReasons.ItemLogReason.NEW_PLAYER_MISSION_BOX_REWARD);

                // ga 打点
                biLogUtil.newPlayerMissionBoxReward(role, String.valueOf(mateId), 2, drop);
            }

            record.set(index, PsRewardClaimStatus.ALREADY_BE_CLAIMED);
            newPlayerMission.setVipRewards(record);
        } else {
            var record = newPlayerMission.getCommonRewards();
            if (record.get(index) != PsRewardClaimStatus.CAN_BE_CLAIMED) {
                var msg = toMissionBoxRewardMsg(mateId, vip, PsErrorCode.ERROR, new ArrayList<>());
                role.send(msg);
                return;
            }

            var reward = mate.getReward();
            // 发奖
            if (StringUtils.isNotBlank(reward)) {
                List<SimpleItem> drop = srvDpd.getDropService().drop(reward);
                drop.forEach(simpleItem -> {
                    rewardList.add(simpleItem.toPsObject());
                });
                srvDpd.getItemService().give(role, drop, LogReasons.ItemLogReason.NEW_PLAYER_MISSION_BOX_REWARD);

                // ga 打点
                biLogUtil.newPlayerMissionBoxReward(role, String.valueOf(mateId), 1, drop);
            }

            record.set(index, PsRewardClaimStatus.ALREADY_BE_CLAIMED);
            newPlayerMission.setCommonRewards(record);
        }

        missionManager.saveNewPlayerMission(newPlayerMission);
        var msg = toMissionBoxRewardMsg(mateId, vip, PsErrorCode.SUCCESS, rewardList);
        role.send(msg);
    }

    public GcCrownedKingMissionList toMissionListMsg(NewPlayerMission newPlayerMission) {
        GcCrownedKingMissionList msg = new GcCrownedKingMissionList();
        msg.setMissions(new HashMap<>());
        if (newPlayerMission == null) {
            return msg;
        }

        for (var entry : newPlayerMission.getMissions().entrySet()) {
            var list = msg.getMissions().get(entry.getKey());
            if (list == null) {
                list = new ArrayList<>();
            }
            for (var m : entry.getValue().values()) {
                list.add(m.toMsg());
            }
            msg.putToMissions(entry.getKey(), list);
        }
        msg.setDay(newPlayerMission.getUnlockDay());
        msg.setScore(newPlayerMission.getPoint());
        newPlayerMission.getVipRewards().forEach(msg::addToVipRewards);
        newPlayerMission.getCommonRewards().forEach(msg::addToCommonRewards);
        msg.setExpireTime(newPlayerMission.getEndTime());

        return msg;
    }

    public static GcCrownedKingMissionUpdate toMissionUpdateMsg(List<NewPlayerMissionItem> missionItems, NewPlayerMission newPlayerMission) {
        GcCrownedKingMissionUpdate msg = new GcCrownedKingMissionUpdate();
        if (missionItems != null && !missionItems.isEmpty()) {
            missionItems.forEach(v -> {
                msg.addToMissions(MissionServiceImpl.toInfo(v));
            });
        }
        msg.setDay(newPlayerMission.getUnlockDay());
        msg.setScore(newPlayerMission.getPoint());
        newPlayerMission.getVipRewards().forEach(msg::addToVipRewards);
        newPlayerMission.getCommonRewards().forEach(msg::addToCommonRewards);
        return msg;
    }

    public static GcReceiveCrownedKingMissionReward toMissionRewardMsg(String metaId, PsErrorCode result, List<SimpleItem> list) {
        GcReceiveCrownedKingMissionReward msg = new GcReceiveCrownedKingMissionReward();
        msg.setId(metaId);
        msg.setCode(result);
        if (list.isEmpty()) {
            msg.setItemRewards(new ArrayList<>());
        } else {
            list.forEach(m -> {
                msg.addToItemRewards(m.toPsObject());
            });
        }
        return msg;
    }

    public static GcReceiveCrownedKingMissionBoxReward toMissionBoxRewardMsg(int index, boolean vip, PsErrorCode result, ArrayList<PsSimpleItem> list) {
        var msg = new GcReceiveCrownedKingMissionBoxReward();
        msg.setRewardsIndex(index);
        msg.setVip(vip);
        msg.setCode(result);
        if (list.isEmpty()) {
            msg.setItemRewards(new ArrayList<>());
        } else {
            msg.setItemRewards(list);
        }
        return msg;
    }

    public void openNewPlayerMission(Role role) {
        if (!functionSwitchService.isOpen(FunctionType.NOVICE.getId(), role)) {
            return;
        }
        if (role == null) {
            return;
        }

        NewPlayerMission newPlayerMission = missionManager.getNewPlayerMission(role.getId());
        if (newPlayerMission == null) {
            newPlayerMission = newPlayerMissionDao.create(role);
            newPlayerMissionDao.save(newPlayerMission);
        }

        // 不重复开启
        if (newPlayerMission.isOpen()) {
            return;
        }

        var activity = activityDao.findActivityByActivityType(ActivityType.CROWNED_KING);

        ErrorLogUtil.errorLog("activity not found", "活动名",ActivityType.CROWNED_KING.name());
        if (activity == null) {
            ErrorLogUtil.errorLog("activity not found", "活动名",ActivityType.CROWNED_KING.name());
            return;
        }
        var activityMeta = configService.getConfig(ActivityListConfig.class).getMetaById(activity.getMetaId());
        if (activityMeta == null) {
            ErrorLogUtil.errorLog("activity not found", "活动名",ActivityType.CROWNED_KING.name());
            return;
        }
        var timeSects = activityMeta.getTimeSects();
//        if (timeSects.length != 1 || timeSects[0].length != 1) {
//            logger.error("activity config format error : {}", ActivityType.CROWNED_KING.name());
//            return;
//        }
        var activityExpired = timeSects[0][timeSects[0].length - 1];

        // 开启任务
        newPlayerMission.open(role, activityExpired);

        // 发送任务列表
        sendNewPlayerMissionMsg(role);
    }
}
