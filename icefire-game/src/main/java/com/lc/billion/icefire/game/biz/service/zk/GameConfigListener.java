package com.lc.billion.icefire.game.biz.service.zk;

import java.io.IOException;

import com.lc.billion.icefire.csacontrol.biz.service.rpc.CSAControlRPCToGameProxyService;
import com.lc.billion.icefire.game.biz.service.impl.functionSwitch.FunctionSwitchService;
import com.lc.billion.icefire.game.biz.service.impl.functionSwitch.FunctionType;
import com.lc.billion.icefire.game.biz.service.rpc.*;
import com.lc.billion.icefire.gvgbattle.biz.service.rpc.TVTBattleRPCToTVTControlProxyService;
import com.lc.billion.icefire.kvkcontrol.biz.service.KVKControlService;
import com.lc.billion.icefire.tvtcontrol.biz.service.rpc.TVTControlRPCToTVTBattleProxyService;
import com.lc.billion.icefire.tvtcontrol.biz.service.rpc.TvtControlRpcToGameProxyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.BeanCreationNotAllowedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.ServerConfigManager;
import com.lc.billion.icefire.game.biz.dao.jongo.multi.MultiJongoClient;
import com.lc.billion.icefire.game.biz.service.impl.world.WorldServiceImpl;
import com.lc.billion.icefire.gvgbattle.biz.service.rpc.GVGBattleRPCToGVGControlProxyService;
import com.lc.billion.icefire.gvgbattle.biz.service.rpc.GVGBattleRPCToGameProxyService;
import com.lc.billion.icefire.gvgcontrol.biz.service.rpc.GVGControlRPCToGVGBattleProxyService;
import com.lc.billion.icefire.gvgcontrol.biz.service.rpc.GVGControlRPCToGameProxyService;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.config.ServerTypeConfig;
import com.longtech.ls.config.WorldMapConfig;
import com.longtech.ls.zookeeper.ConfigCenter;
import com.longtech.ls.zookeeper.GameServerConfig;
import com.longtech.ls.zookeeper.IZkGameServerConfigListener;

/**
 * <AUTHOR>
 *
 */
@Service
public class GameConfigListener implements IZkGameServerConfigListener {

	@Autowired
	private ApplicationContext appCtx;

	protected Logger logger = LoggerFactory.getLogger(getClass());

	@Override
	public void onStarted(int serverId) {
		getBean(GameRPCToGameProxyService.class).startRpcFor(serverId);
		getBean(GameRPCToGVGControlProxyService.class).startRpcFor(serverId);
		getBean(GameRPCToGVGBattleProxyService.class).startRpcFor(serverId);
		getBean(GVGBattleRPCToGameProxyService.class).startRpcFor(serverId);

		getBean(GVGBattleRPCToGVGControlProxyService.class).startRpcFor(serverId);
		getBean(GVGControlRPCToGameProxyService.class).startRpcFor(serverId);
		getBean(GVGControlRPCToGVGBattleProxyService.class).startRpcFor(serverId);

		getBean(TVTBattleRPCToTVTControlProxyService.class).startRpcFor(serverId);
		getBean(TvtControlRpcToGameProxyService.class).startRpcFor(serverId);
		getBean(TVTControlRPCToTVTBattleProxyService.class).startRpcFor(serverId);
		getBean(GameRPCToTvtControlProxyService.class).startRpcFor(serverId);


		if (getBean(FunctionSwitchService.class).isOpen(FunctionType.CROSS_SERVER_ATTACK)) {
			logger.info("start rpc csa control");
			getBean(GameRPCToCSAControlProxyService.class).startRpcFor(serverId);
			getBean(CSAControlRPCToGameProxyService.class).startRpcFor(serverId);
		}
		// getBean(KvkSeasonRPCToGameProxyService.class).startRpcFor(serverId);
		// getBean(GameRPCToKvkSeasonProxyService.class).startRpcFor(serverId);
		// getBean(KVKControlRPCToGameProxyService.class).startRpcFor(serverId);
		// getBean(GameRPCToKVKControlProxyService.class).startRpcFor(serverId);
	}

	@Override
	public void onStoped(int serverId) {
		getBean(GameRPCToGameProxyService.class).stopRpcFor(serverId);
		getBean(GameRPCToGVGControlProxyService.class).stopRpcFor(serverId);
		getBean(GameRPCToGVGBattleProxyService.class).stopRpcFor(serverId);
		getBean(GVGBattleRPCToGameProxyService.class).stopRpcFor(serverId);

		getBean(GVGBattleRPCToGVGControlProxyService.class).stopRpcFor(serverId);
		getBean(GVGControlRPCToGameProxyService.class).stopRpcFor(serverId);
		getBean(GVGControlRPCToGVGBattleProxyService.class).stopRpcFor(serverId);

		getBean(TVTBattleRPCToTVTControlProxyService.class).stopRpcFor(serverId);
		getBean(TvtControlRpcToGameProxyService.class).stopRpcFor(serverId);
		getBean(TVTControlRPCToTVTBattleProxyService.class).stopRpcFor(serverId);
		getBean(GameRPCToTvtControlProxyService.class).stopRpcFor(serverId);

		if (getBean(FunctionSwitchService.class).isOpen(FunctionType.CROSS_SERVER_ATTACK)) {
			getBean(GameRPCToCSAControlProxyService.class).stopRpcFor(serverId);
			getBean(CSAControlRPCToGameProxyService.class).stopRpcFor(serverId);
		}

		// getBean(KvkSeasonRPCToGameProxyService.class).stopRpcFor(serverId);
		// getBean(GameRPCToKvkSeasonProxyService.class).stopRpcFor(serverId);
		// getBean(KVKControlRPCToGameProxyService.class).stopRpcFor(serverId);
		// getBean(GameRPCToKVKControlProxyService.class).stopRpcFor(serverId);
	}

	@Override
	public void onRestartTimeMsChanged(int serverId) {
		// 目前只要有变化通知就重启，暂不实现 延时重启。
		if (serverId == Application.getServerId()) {
			logger.info("执行restart脚本");
			doShell("restart");
		} else {
			logger.info("serverId不一致serverId1={},serverId2={}", serverId, Application.getServerId());
		}
	}

	@Override
	public void onStopTimeMsChanged(int serverId) {
		// 目前只要有变化通知就停服，暂不实现 延时停服。
		if (serverId == Application.getServerId()) {
			logger.info("收到停服信息，开始停服{}", serverId);
			doShell("stop");
		}
	}

	private void doShell(String cmd) {
		try {
			// Java执行本地命令是启用一个子进程处理,默认情况下子进程与父进程I/O通过管道相连(默认ProcessBuilder.Redirect.PIPE)
			// 当服务执行自身重启的命令时,父进程关闭导致管道连接中断,将导致子进程也崩溃,从而无法完成后续的启动
			// 解决方式,(1)设置子进程IO输出重定向到指定文件;(2)设置属性子进程的I/O源或目标将与当前进程的相同,两者相互独立
			Process p = new ProcessBuilder("sh", cmd + ".sh").redirectOutput(ProcessBuilder.Redirect.INHERIT).redirectError(ProcessBuilder.Redirect.INHERIT)
					.redirectInput(ProcessBuilder.Redirect.INHERIT).redirectErrorStream(true).start();
			// 下列不能打开，会卡死
			// BufferedReader br = new BufferedReader(new
			// InputStreamReader(p.getInputStream()));
			// String str = null;
			// while ((str = br.readLine()) != null) {
			// log.info(str);
			// }
			// br.close();
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	@Override
	public void onAdded(int serverId) {
		// 如果本服是游戏服~增加的服务器是gvg服务器~不创建新的mongo连接
		WorldServiceImpl worldService = getBean(WorldServiceImpl.class);
		ServerType currentServerType = null;
		WorldMapConfig worldMapConfig = ServerConfigManager.getInstance().getWorldMapConfig();
		if (worldMapConfig != null) {
			currentServerType = worldMapConfig.getServerType();
		}
		ServerType targetServerType = null;
		ServerTypeConfig serverTypeConfig = ServerConfigManager.getInstance().getServerTypeConfig();
		if (serverTypeConfig != null) {
			targetServerType = serverTypeConfig.getServerType(serverId);
		}
		ConfigCenter configCenter = getBean(ConfigCenter.class);
		int currentServerId = Application.getServerId();
		logger.info("服 {} mongo连接添加, 本服服务器类型 {}, 目标服服务器类型 {}", serverId, currentServerType, targetServerType);
		if ((configCenter.currentServerTypeIsGAME_or_KVKSEASON() && (worldService.isBattleServer(serverId)))
				|| (currentServerId != serverId && worldService.isBattleServer(currentServerId) && worldService.isBattleServer(serverId))) {
			logger.info("当前服：{} 不持有战斗服 {} 的连接。。", currentServerId, serverId);
			return;
		}
		// 动态增加DB
		GameServerConfig gsc = configCenter.getLsConfig().getGameServers().get(serverId);
		getBean(MultiJongoClient.class).addJongoClient(String.valueOf(serverId), gsc.getMongoDb().getUrl(), gsc.getMongoDb().getDbName());
		if (currentServerType == ServerType.KVK_CONTROL && serverTypeConfig.getServerType(serverId) == ServerType.KVK_SEASON) {
			logger.info("新增了一个赛季服，需要zk导入赛季的配置");
			Application.getBean(KVKControlService.class).calculateKVKSeason();
		}
	}

	@Override
	public void onRemoved(int serverId) {
		this.onStoped(serverId);
		int currentServerId = Application.getServerId();
		if (currentServerId != serverId) {
			// 动态减少DB
			getBean(MultiJongoClient.class).removeJongoClient(String.valueOf(serverId));
		}
	}

	private <T> T getBean(Class<T> beanClass) {
		try {
			return appCtx.getBean(beanClass);
		} catch (BeanCreationNotAllowedException e) {
			throw new RuntimeException("由于spring正在销毁，导致appCtx.getBean(MultiJongoClient.class)发生异常！", e);
		}
	}

	@Override
	public void onRpcIpChanged(int serverId) {
		getBean(GameRPCToGameProxyService.class).onRpcIpChanged(serverId);
		getBean(GameRPCToGVGControlProxyService.class).onRpcIpChanged(serverId);
		getBean(GameRPCToGVGBattleProxyService.class).onRpcIpChanged(serverId);
		getBean(GVGBattleRPCToGameProxyService.class).onRpcIpChanged(serverId);

		getBean(GVGBattleRPCToGVGControlProxyService.class).onRpcIpChanged(serverId);
		getBean(GVGControlRPCToGameProxyService.class).onRpcIpChanged(serverId);
		getBean(GVGControlRPCToGVGBattleProxyService.class).onRpcIpChanged(serverId);

		getBean(TVTBattleRPCToTVTControlProxyService.class).onRpcIpChanged(serverId);
		getBean(TvtControlRpcToGameProxyService.class).onRpcIpChanged(serverId);
		getBean(TVTControlRPCToTVTBattleProxyService.class).onRpcIpChanged(serverId);
		getBean(GameRPCToTvtControlProxyService.class).onRpcIpChanged(serverId);

		if (getBean(FunctionSwitchService.class).isOpen(FunctionType.CROSS_SERVER_ATTACK)) {
			getBean(GameRPCToCSAControlProxyService.class).onRpcIpChanged(serverId);
			getBean(CSAControlRPCToGameProxyService.class).onRpcIpChanged(serverId);
		}
		// getBean(KvkSeasonRPCToGameProxyService.class).onRpcIpChanged(serverId);
		// getBean(GameRPCToKvkSeasonProxyService.class).onRpcIpChanged(serverId);
		// getBean(KVKControlRPCToGameProxyService.class).onRpcIpChanged(serverId);
		// getBean(GameRPCToKVKControlProxyService.class).onRpcIpChanged(serverId);

	}

	@Override
	public void onRpcPortChanged(int serverId) {
		getBean(GameRPCToGameProxyService.class).onRpcPortChanged(serverId);
		getBean(GameRPCToGVGControlProxyService.class).onRpcPortChanged(serverId);
		getBean(GameRPCToGVGBattleProxyService.class).onRpcPortChanged(serverId);
		getBean(GVGBattleRPCToGameProxyService.class).onRpcPortChanged(serverId);

		getBean(GVGBattleRPCToGVGControlProxyService.class).onRpcPortChanged(serverId);
		getBean(GVGControlRPCToGameProxyService.class).onRpcPortChanged(serverId);
		getBean(GVGControlRPCToGVGBattleProxyService.class).onRpcPortChanged(serverId);

		getBean(TVTBattleRPCToTVTControlProxyService.class).onRpcPortChanged(serverId);
		getBean(TvtControlRpcToGameProxyService.class).onRpcPortChanged(serverId);
		getBean(TVTControlRPCToTVTBattleProxyService.class).onRpcPortChanged(serverId);
		getBean(GameRPCToTvtControlProxyService.class).onRpcPortChanged(serverId);

		if (getBean(FunctionSwitchService.class).isOpen(FunctionType.CROSS_SERVER_ATTACK)) {
			getBean(GameRPCToCSAControlProxyService.class).onRpcPortChanged(serverId);
			getBean(CSAControlRPCToGameProxyService.class).onRpcPortChanged(serverId);
		}

		// getBean(KvkSeasonRPCToGameProxyService.class).onRpcPortChanged(serverId);
		// getBean(GameRPCToKvkSeasonProxyService.class).onRpcPortChanged(serverId);
		// getBean(KVKControlRPCToGameProxyService.class).onRpcPortChanged(serverId);
		// getBean(GameRPCToKVKControlProxyService.class).onRpcPortChanged(serverId);
	}

}
