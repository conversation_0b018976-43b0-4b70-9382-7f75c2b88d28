package com.lc.billion.icefire.game.biz.service.impl.rank.impl;

import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.config.ItemConfig;
import com.lc.billion.icefire.game.biz.config.SettingConfig;
import com.lc.billion.icefire.game.biz.dao.impl.RankDaoImpl;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.RoleCacheDao;
import com.lc.billion.icefire.game.biz.manager.SkinManager;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.cache.RoleCache;
import com.lc.billion.icefire.game.biz.redis.RedisDataType;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.item.ItemServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.rank.IRank;
import com.lc.billion.icefire.game.biz.service.impl.rank.RankServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.rank.RankType;
import com.lc.billion.icefire.game.biz.service.impl.unlock.UnlockServiceImpl;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.lc.billion.icefire.game.support.LogReasons;
import com.lc.billion.icefire.protocol.constant.UnlockModule;
import com.simfun.sgf.utils.JavaUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import redis.clients.jedis.resps.Tuple;

/**
 * <AUTHOR>
 */
public abstract class AbstractRank implements IRank {
    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    protected RankDaoImpl rankDao;
    @Autowired
    protected RankServiceImpl rankService;
    @Autowired
    protected ItemServiceImpl itemService;
    @Autowired
    protected RoleDao roleDao;
    @Autowired
    protected SkinManager skinManager;
    @Autowired
    protected ServiceDependency srvDpd;
    @Autowired
    protected ConfigServiceImpl configSrv;
    @Autowired
    protected UnlockServiceImpl unlockService;
    @Autowired
    protected RoleCacheDao roleCacheDao;

    /**
     * 获取排行
     *
     * @param type     排行榜类型
     * @param memberId 成员
     * @return 排名
     */
    @Override
    public int getRank(RankType type, String memberId, String... params) {
        Long rank = rankDao.getRank(getKey(type, params), memberId, type.getRedisType());
        if (rank == null) {
            return 0;
        }
        return rank.intValue() + 1;
    }

    /**
     * 获取分数
     *
     * @param type     排行榜类型
     * @param memberId 成员
     * @param params   额外参数
     * @return 分数
     */
    @Override
    public long getScore(RankType type, String memberId, String... params) {
        Long score = rankDao.getScore(getKey(type, params), memberId, type.getRedisType());
        if (score == null) {
            return 0;
        }
        return score;
    }

    public boolean hasTitle(RankType type) {
        // 个人击杀、火炉等级、个人击杀、讨伐关卡、武将战力、武将总战力、个人科技
        return type == RankType.ROLE_FIGHT || type == RankType.CITY_LEVEL || type == RankType.ROLE_KILLS ||
                type == RankType.CHAPTER_LEVEL_RANK || type == RankType.PERSONAL_HERO_POWER_RANK || type == RankType.HERO_TOTAL ||
                type == RankType.ROLE_RESEARCH_RANK;
    }

    public String getTitleId(RankType type) {
        if(!hasTitle(type)) {
            return "";
        }
        int rankTitle = 0;
        // 1：个人战力；2:火炉等级；3：个人歼灭；4：讨伐关卡；5：武力战力；6：武将总战力；7：科技总战力
        switch (type) {
            case ROLE_FIGHT:
                rankTitle = 1;
                break;
            case CITY_LEVEL:
                rankTitle = 2;
                break;
            case ROLE_KILLS:
                rankTitle = 3;
                break;
            case CHAPTER_LEVEL_RANK:
                rankTitle = 4;
                break;
            case PERSONAL_HERO_POWER_RANK:
                rankTitle = 5;
                break;
            case HERO_TOTAL:
                rankTitle = 6;
                break;
            case ROLE_RESEARCH_RANK:
                rankTitle = 7;
                break;
            default:
                break;
        }
        return configSrv.getConfig(SettingConfig.class).getRankFashionId(rankTitle);
    }

    // 计算 useNow 类型的道具，使用后的 道具Id
    private String itemIdAfterUseNow(String originItemId) {
        try {
            ItemConfig itemCfg = srvDpd.getConfigService().getConfig(ItemConfig.class);
            ItemConfig.ItemMeta itemMeta = itemCfg.get(originItemId);
            String param1 = itemMeta.getParam1();
            String[] param1Str = StringUtils.split(param1, '|');
            return param1Str[0];
        } catch (Exception e) {
            if (!(e instanceof ExpectedException)) {
                ErrorLogUtil.exceptionLog("rank fashion itemIdAfterUseNow fail", e);
            }
            return "";
        }
    }

    // 赛季切换时，移除上一个赛季 4个原服的榜一称号
    // 由于此时读取的已经不是原服redis, 只能无差异所有用户移除一遍
    public void removeLastSeasonTitle(RankType rankType) {
        logger.warn("移除原服称号 {} 开始", rankType);
        String itemId = getTitleId(rankType);
        if(itemId == null || itemId.isEmpty()) {
            logger.warn("移除原服称号 {} 没有称号", rankType);
            return;
        }

        String itemIdAfterUseNow = itemIdAfterUseNow(itemId);
        if(itemIdAfterUseNow.isEmpty()) { // 发的道具不是皮肤
            logger.warn("移除原服称号 {} 配置不是皮肤", rankType);
            return;
        }

        for (var role : roleDao.findAll()) {
            if(role == null) continue;
            if(skinManager.hasSkin(role, itemIdAfterUseNow)) {
                skinManager.lockSkin(role, itemIdAfterUseNow);
                logger.info("移除原服称号 rankType={} role={} itemIdAfterUseNow={}", rankType, role.getRoleId(), itemIdAfterUseNow);
            }
        }
        logger.warn("移除原服称号 {} 结束", rankType);
    }

    // 需要发送排行榜称号的子类，在定时flush结束后，调用我
    public boolean sendRankFashion(RankType rankType, Tuple lastChampion, long maxMember, String... params) {
        if(maxMember == 0) return false;
        // 战场服不走这儿
        if (Application.isGVGBattleServer()) {
            return false;
        }
        String itemId = getTitleId(rankType);
        if(itemId == null || itemId.isEmpty()) {
            logger.warn(rankType.toString() + " has no title");
            return false;
        }

        String lastMember = "";
        if(lastChampion != null) {
            lastMember = lastChampion.getElement();
        }

        String itemIdAfterUseNow = itemIdAfterUseNow(itemId);
        if(itemIdAfterUseNow.isEmpty()) {
            logger.info("rank fashion skip to deduct title：{}, itemId {}", rankType, itemId);
        }

        Role lastChampionRole = JavaUtils.bool(lastMember) ? roleDao.findById(Long.valueOf(lastMember)) : null;
        if (lastChampionRole != null) {
            String maxMemberStr = maxMember + "";
            if(lastMember.equals(maxMemberStr) && skinManager.hasSkin(lastChampionRole, itemIdAfterUseNow)) { // 榜一没有换人, 且道具还在
                logger.info("rank fashion no change ：{}, roleId {}", rankType, maxMember);
                return true;
            }
            // 榜一换人了，扣掉原先的， 或者是没换人，但是旧版本道具过期了
            skinManager.lockSkin(lastChampionRole, itemIdAfterUseNow);
        }  else {
            if (JavaUtils.bool(lastMember)) {
                RoleCache roleCache = roleCacheDao.findById(Long.parseLong(lastMember));
                if (roleCache != null) {
                    logger.info("rank fashion lastChampionRole move,  lastMember {}", lastMember);
                    return false;
                }
            }

        }
        // 再给新榜一发道具
        Role toRole = roleDao.findById(maxMember);
        if (toRole == null) {
            logger.info("rank fashion toRole null,  toRoleId {}", maxMember);
            return false;
        }
        if (!unlockService.isUnlock(toRole, UnlockModule.RANK_FASHION)) {
            logger.info("rank fashion lock roleId {} , skip to give title", toRole.getRoleId());
            return false;
        }
        if (skinManager.hasSkin(toRole, itemIdAfterUseNow)) {
            logger.info("rank fashion role repeated {} , skip to give title", toRole.getRoleId());
            return false;
        }
        itemService.give(toRole, itemId, 1, LogReasons.ItemLogReason.WIN_RANK_CHAMPION);
        logger.info(rankType.toString() + " rank_fashion " + itemId + " has send to " + maxMember);
        return true;
    }

    /**
     * 更新成员排行榜分数
     *
     * @param rankType 排行榜类型
     * @param member   成员
     * @param score    分数
     */
    @Override
    public void updateRankScore(RankType rankType, String member, long score, String... params) {
        rankDao.asyncUpdateRank(getKey(rankType, params), score, member, rankType.getRedisType());
    }
    @Override
    public void updateRankScore(RankType rankType, int serverId, String member, long score, String... params) {
        rankDao.asyncUpdateRank(getKey(rankType, serverId, params), score, member, rankType.getRedisType());
    }

    /**
     * 增加成员排行榜分数
     *
     * @param rankType 排行榜类型
     * @param member   成员
     * @param score    分数
     */
    @Override
    public void addRankScore(RankType rankType, String member, long score, String... params) {
        rankDao.asyncIncScore(getKey(rankType, params), score, member, rankType.getRedisType());
    }

    @Override
    public void addRankScore(RankType rankType, String member, long score, long achieveTime, String... params) {
        rankDao.asyncIncScore(getKey(rankType, params), score, achieveTime, member, rankType.getRedisType());
    }

    /**
     * 删除成员排行榜的信息
     *
     * @param rankType 排行榜类型
     * @param member   成员
     */
    @Override
    public void deleteMember(RankType rankType, String member, String... params) {
        rankDao.asyncDeleteRank(getKey(rankType, params), member, rankType.getRedisType());
    }

    @Override
    public void deleteGroupMember(RankType rankType, int group, String member, String... params) {
        rankDao.asyncDeleteRank(getGroupKey(rankType, group, params), member, RedisDataType.CROSS_SERVER_RANK);
    }

    /**
     * 删除排行榜的信息
     *
     * @param rankType 排行榜类型
     */
    @Override
    public void deleteRank(RankType rankType, String... params) {
        rankDao.asyncClearRank(getKey(rankType, params), rankType.getRedisType());
    }

    @Override
    public Long getCount(RankType rankType, String key, double min, double max) {
        return rankDao.getCount(key, rankType.getRedisType(), min, max);
    }

    public String getMemberId(long roleId) {
        throw new AlertException("UnsupportedOperation");
    }

    /**
     * 清理排行榜脏数据
     *
     * @param rankType 排行榜类型
     * @param params   额外参数
     */
    @Override
    public void clean(RankType rankType, String... params) {

    }
    @Override
    public void clean(RankType rankType, int serverId, String... params) {

    }

    /**
     * 个人排行榜
     * 用来表示：这个排行榜，是否在玩家被清理出内存时，删除
     *
     * @return 是否是个人排行榜
     */
    @Override
    public boolean isRoleSelf() {
        return false;
    }

    /**
     * 公会排行榜
     *
     * @return 是否是公会排行榜
     */
    @Override
    public boolean isAlliance() {
        return false;
    }

    /**
     * 是否有旧数据
     *
     * @return 是否有旧数据
     */
    @Override
    public boolean hasOld() {
        return false;
    }

    /**
     * 更新成员排行榜分数
     *
     * @param rankType 排行榜类型
     * @param member   成员
     * @param score    分数
     */
    @Override
    public void updateRankScore(RankType rankType, String member, long score, long achieveTime, String... params) {
        rankDao.asyncUpdateRank(getKey(rankType, params), score, achieveTime, member, rankType.getRedisType());
    }
    @Override
    public void updateRankScore(RankType rankType, int serverId, String member, long score, long achieveTime, String... params) {
        rankDao.asyncUpdateRank(getKey(rankType, serverId, params), score, achieveTime, member, rankType.getRedisType());
    }

    @Override
    public void updateGroupRankScore(RankType rankType, int group, String member, double score, String... params) {
        rankDao.asyncUpdateGroupRank(getGroupKey(rankType, group, params), member, score, RedisDataType.CROSS_SERVER_RANK);
    }
}
