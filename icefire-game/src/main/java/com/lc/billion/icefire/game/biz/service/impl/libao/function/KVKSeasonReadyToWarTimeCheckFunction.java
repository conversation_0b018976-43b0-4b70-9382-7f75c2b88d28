package com.lc.billion.icefire.game.biz.service.impl.libao.function;

import com.lc.billion.icefire.game.biz.config.LiBaoConfig.LibaoMeta;
import com.lc.billion.icefire.game.biz.model.libao.LiBaoTimeType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.longtech.ls.zookeeper.ConfigCenter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *  KVK赛季预热到开始战争期间
 * <AUTHOR>
 * @date 2022年5月7日
 */
@Service
public class KVKSeasonReadyToWarTimeCheckFunction implements LiBaoTimeCheck {

	private static final Logger log = LoggerFactory.getLogger(KVKSeasonReadyToWarTimeCheckFunction.class);

	@Autowired
	private ConfigCenter configCenter;


	@Override
	public LiBaoTimeType getType() {
		return LiBaoTimeType.KVK_SEASON_READY_TO_WAR;
	}

	@Override
	public boolean check(Role role, LibaoMeta meta) {
        return false;
//		if(Application.getSeason() <= 1){
//			return false;
//		}
//		KvkSeasonServerGroupConfig serverGroup = configCenter.getCurrentKvkSeasonServerGroupConfig();
//		if (serverGroup == null || serverGroup.getSeason() != meta.getTimeTypeSeasonId()){
//			// 没有赛季信息 或者 赛季条件不匹配
//			return false;
//		}
//		return System.currentTimeMillis() >= serverGroup.getReadyTime() && System.currentTimeMillis() <= serverGroup.getBattleTime();
	}

	/**
	 *  赛季信息不匹配 返回 -1
	 * @param meta
	 * @param serverGroup
	 * @return 礼包开始时间
	 */
	public long getStartTime(LibaoMeta meta){
        return -1;
//		if(Application.getSeason() <= 1)
//			return -1;
//		KvkSeasonServerGroupConfig serverGroup = configCenter.getCurrentKvkSeasonServerGroupConfig();
//		if (serverGroup == null){
//			// 没有赛季信息 或者 赛季条件不匹配
//			return -1;
//		}
//		return serverGroup.getReadyTime();
	}

	/**
	 *  赛季信息不匹配 返回 -1
	 * @param meta
	 * @param serverGroup
	 * @return 礼包结束时间
	 */
	public long getEndTime(LibaoMeta meta){
        return -1;
//		if(Application.getSeason() <= 1)
//			return -1;
//		KvkSeasonServerGroupConfig serverGroup = configCenter.getCurrentKvkSeasonServerGroupConfig();
//		if (serverGroup == null){
//			// 没有赛季信息 或者 赛季条件不匹配
//			return -1;
//		}
//		return serverGroup.getBattleTime();
	}



}
