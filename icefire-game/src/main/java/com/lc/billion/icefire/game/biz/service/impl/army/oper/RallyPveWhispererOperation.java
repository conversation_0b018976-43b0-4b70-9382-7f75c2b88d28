package com.lc.billion.icefire.game.biz.service.impl.army.oper;

import com.lc.billion.icefire.game.biz.dao.mongo.root.WhispererNodeDao;
import com.lc.billion.icefire.game.biz.manager.activity.WhispererActivityManager;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.army.ArmyType;
import com.lc.billion.icefire.game.biz.model.email.EmailConstants;
import com.lc.billion.icefire.game.biz.model.scene.SceneNode;
import com.lc.billion.icefire.game.biz.model.scene.SceneNodeType;
import com.lc.billion.icefire.game.biz.model.scene.node.WhispererNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020/10/9
 */
@Service
public class RallyPveWhispererOperation extends AbstractArmyFightOperation {
	@Autowired
	private WhispererActivityManager activityManager;
	@Autowired
	private WhispererNodeDao nodeDao;

	@Override
	public void finishWork(ArmyInfo attackerMainArmy) {
		super.finishWork(attackerMainArmy);
		armyManager.marchRetBILog(attackerMainArmy);
		armyManager.returnArmy(attackerMainArmy);
	}

	@Override
	protected void endBattle(ArmyInfo armyInfo) {
		boolean win = armyInfo.getFightContext().isWin();
		if (win) {
			handleWin(armyInfo);
		} else {
			armyManager.returnStamina(armyInfo, EmailConstants.RETURN_STAMINA_NONE);
		}
	}

	/**
	 * 战斗胜利处理
	 *
	 * @param armyInfo
	 */
	private void handleWin(ArmyInfo armyInfo) {
		SceneNode targetNode = armyManager.getArmyTargetNode(armyInfo);
		if (targetNode == null || targetNode.getNodeType() != SceneNodeType.WHISPERER) {
			return;
		}
		WhispererNode node = (WhispererNode) targetNode;
		// 更新活动记录进度
		activityManager.onWhispererBattleWin(armyInfo.getRoleId(),node);
		srvDpd.getWhispererActivityService().handlePveReward(armyInfo, node);
		// 删除node
		sceneService.remove(node, false);
		nodeDao.delete(node);
	}

	@Override
	public ArmyType getArmyType() {
		return ArmyType.RALLY_PVE_WHISPERER;
	}

	@Override
	protected boolean check(ArmyInfo army, SceneNode targetNode) {
		boolean suc = super.check(army, targetNode);
		if (!suc) {
			return false;
		}
		if (!activityManager.isOpen()) {
			return false;
		}
		WhispererNode whisperer = (WhispererNode) targetNode;
		return whisperer.checkCanAttack(army.getOwner());
	}

	@Override
	public void returnArrived(ArmyInfo army) {
		armyManager.takeBackArmy(army);
	}

	@Override
	protected String getReturnStaminaMailId() {
		return EmailConstants.PVE_STAMINA_TURN_BACK_2;
	}
}
