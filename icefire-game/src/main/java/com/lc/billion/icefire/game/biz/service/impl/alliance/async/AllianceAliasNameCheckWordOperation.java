package com.lc.billion.icefire.game.biz.service.impl.alliance.async;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.protocol.GcAllianceShortNameExist;

public class AllianceAliasNameCheckWordOperation extends AsyncCheckWordOperation {
	/** 简称 */
	private String aliasName;
	private boolean exist;

	public AllianceAliasNameCheckWordOperation(Role role, boolean exist, String aliasName) {
		this.role = role;
		this.aliasName = aliasName;
		this.exist = exist;
	}

	@Override
	public boolean init() {
		return true;
	}

	@Override
	public boolean run() {
		setWord(aliasName);
		return super.run();
	}

	@Override
	public void finish() {
		GcAllianceShortNameExist gcAllianceShortNameExist = new GcAllianceShortNameExist();
		gcAllianceShortNameExist.setExist(exist);
		if (isSpam()) {
			gcAllianceShortNameExist.setLegitimate(false);
			role.send(gcAllianceShortNameExist);
			return;
		}

		gcAllianceShortNameExist.setLegitimate(true);
		role.send(gcAllianceShortNameExist);
	}
}
