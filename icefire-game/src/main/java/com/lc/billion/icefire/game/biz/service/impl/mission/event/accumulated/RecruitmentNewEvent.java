package com.lc.billion.icefire.game.biz.service.impl.mission.event.accumulated;

import com.lc.billion.icefire.game.biz.config.CardTypeNConfig;
import com.lc.billion.icefire.game.biz.config.MissionConfig;
import com.lc.billion.icefire.game.biz.model.mission.IMissionItem;
import com.lc.billion.icefire.game.biz.model.mission.IMissionItem;
import com.lc.billion.icefire.game.biz.model.mission.AbstractBaseMissionItem;
import com.lc.billion.icefire.game.biz.model.mission.MissionType;
import com.lc.billion.icefire.game.biz.service.impl.lottery.LotteryType;
import com.lc.billion.icefire.game.biz.service.impl.mission.event.AbstractMissionEvent;

/**
 * 玩家招募次数
 */
public class RecruitmentNewEvent extends AbstractMissionEvent {

    @Override
    public MissionType getType() {
        return MissionType.RECRUITMENT_COUNT_NEW;
    }

    @Override
    public boolean check(IMissionItem item, MissionConfig.MissionMeta meta, Object... params) {
        CardTypeNConfig.CardTypeNMeta cardType = (CardTypeNConfig.CardTypeNMeta) params[1];
        // 小禹：我们策划商量了一下，需要修改任务类型的第一个参数，增加一个-1，如果参数是-1，代表卡池1、3、5、6、7、8、9、10的任意招募均可完成该任务；425的第二个参数是次数的逻辑不变
        if (meta.getPara1Int() == -1) {
            int id = cardType.getLotteryType().getId();
            if (id == LotteryType.LOTTERY_ADVANCED.getId()
                    || id == LotteryType.LOTTERY_SERVER_OPEN_TIME.getId()
//                    || id == LotteryType.LOTTERY_TYPE_5.getId()
//                    || id == LotteryType.LOTTERY_TYPE_6.getId()
//                    || id == LotteryType.LOTTERY_TYPE_7.getId()
//                    || id == LotteryType.LOTTERY_TYPE_8.getId()
//                    || id == LotteryType.LOTTERY_TYPE_9.getId()
//                    || id == LotteryType.LOTTERY_TYPE_10.getId()
            ) {
                return true;
            } else {
                return false;
            }
        }
        return meta.getPara1Int() == cardType.getLotteryType().getId();
    }

    @Override
    public boolean effect(IMissionItem item, MissionConfig.MissionMeta meta, Object... params) {
        int addNum = (int) params[2];
        long amount = item.getProgress() + addNum;
        setAmount(item, meta, amount, true);
        return true;
    }
}
