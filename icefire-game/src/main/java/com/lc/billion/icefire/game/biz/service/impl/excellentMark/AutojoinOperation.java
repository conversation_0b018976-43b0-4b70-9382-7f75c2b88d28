package com.lc.billion.icefire.game.biz.service.impl.excellentMark;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.async.BindThreadOperation;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceServiceImpl;


public class AutojoinOperation implements BindThreadOperation {

	private Role role;
	private Alliance recommendAlliance;

	public AutojoinOperation(Role role) {
		this.role = role;
	}

	@Override
	public boolean run() {

		ExcellentMarkService excellentMarkService = Application.getBean(ExcellentMarkService.class);
		recommendAlliance = excellentMarkService.roleGetRecommendedAlliance(role);

		return true;
	}

	@Override
	public void finish() {
		if(recommendAlliance != null){
			AllianceServiceImpl allianceService = Application.getBean(AllianceServiceImpl.class);
			allianceService.requestJoin(role, recommendAlliance.getPersistKey(), "");
		}
	}

	@Override
	public long getBindId() {
		return role.getId();
	}
}
