package com.lc.billion.icefire.game.biz.service.rpc;

import org.springframework.stereotype.Service;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.service.AbstractRPCProxyService;
import com.lc.billion.icefire.rpc.service.gvg.IGameRemoteGVGControlService;
import com.longtech.cod.rpc.client.RpcClient;
import com.longtech.cod.rpc.client.RpcProxyBuilder;
import com.longtech.ls.config.ServerType;
import com.longtech.ls.zookeeper.GameServerConfig;

/**
 * <AUTHOR>
 *
 */
@Service
public class GameRPCToGVGControlProxyService extends AbstractRPCProxyService {

	private RpcProxyBean<IGameRemoteGVGControlService> gameRemoteGVGControlService;

	@Override
	protected ServerType[] getSrcServerType() {
		return new ServerType[] { ServerType.GAME, ServerType.KVK_SEASON };
	}

	@Override
	protected ServerType[] getTargetServerType() {
		return new ServerType[] { ServerType.GVG_CONTROL };
	}

	@Override
	protected boolean createRPCClient(GameServerConfig gameServerConfig) {
		if (gameRemoteGVGControlService == null) {
			RpcProxyBuilder<IGameRemoteGVGControlService> rpcProxyBuilder = RpcProxyBuilder.create(IGameRemoteGVGControlService.class).connect(getSerializer(),
					gameServerConfig.getRpcIp(), gameServerConfig.getRpcPort());
			RpcClient rpcClient = rpcProxyBuilder.createRpcClient();
			IGameRemoteGVGControlService service = rpcProxyBuilder.buildSync(rpcClient, getTimeOutMills(), getRetryTimes(), createWait());
			gameRemoteGVGControlService = new RpcProxyBean<IGameRemoteGVGControlService>(service, rpcClient);
			logger.info("RPC GAME to GVG_CONTROL {}->{},{}:{}", Application.getServerId(), gameServerConfig.getGameServerId(), gameServerConfig.getRpcIp(),
					gameServerConfig.getRpcPort());
		} else {
			logger.info("game to control 的服务已存在，不重复创建");
		}
		return true;
	}

	@Override
	protected void rpcIpChanged(GameServerConfig gameServerConfig) {
		if (gameRemoteGVGControlService != null) {
			RpcClient rpcClient = gameRemoteGVGControlService.getRpcClient();
			rpcClient.setStop(true);
		}
		logger.info("rpcIpChanged {}", gameServerConfig.getGameServerId());
		createRPCClient(gameServerConfig);
	}

	@Override
	protected void rpcPortChanged(GameServerConfig gameServerConfig) {
		logger.info("rpcPortChanged {}", gameServerConfig.getGameServerId());
		rpcIpChanged(gameServerConfig);
	}

	@Override
	protected void removeRPCClient(int serverId) {
		if (gameRemoteGVGControlService != null) {
			RpcClient rpcClient = gameRemoteGVGControlService.getRpcClient();
			rpcClient.setStop(true);
		}
		gameRemoteGVGControlService = null;
	}

	// @Override
	// protected void clearRPCClient() {
	// gameRemoteGVGControlService = null;
	// }

	@Override
	protected boolean containsRPCClient(int serverId) {
		return gameRemoteGVGControlService != null;
	}

	@Override
	protected boolean checkInstance() {
		return false;
	}

	@Override
	protected boolean createWait() {
		return true;
	}

	public IGameRemoteGVGControlService getGameRemoteGVGControlService() {
		if (gameRemoteGVGControlService != null) {
			return gameRemoteGVGControlService.getProxy();
		}
		return null;
	}

}
