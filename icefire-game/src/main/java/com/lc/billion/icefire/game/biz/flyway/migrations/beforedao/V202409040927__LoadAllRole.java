package com.lc.billion.icefire.game.biz.flyway.migrations.beforedao;

import com.lc.billion.icefire.game.biz.flyway.AbstractMongoMigrationForBeforeDao;
import com.lc.billion.icefire.game.biz.flyway.model.FlywayMongoCollection;
import com.lc.billion.icefire.game.biz.model.role.RoleServerInfo;
import com.mongodb.client.model.Updates;
import com.mongodb.client.result.UpdateResult;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @ClassName V202409040927__LoadAllRole
 * @Description
 * <AUTHOR>
 * @Date 2024/9/3 15:27
 * @Version 1.0
 */
public class V202409040927__LoadAllRole extends AbstractMongoMigrationForBeforeDao {
    private final Logger logger = LoggerFactory.getLogger(V202409040927__LoadAllRole.class);

    @Override
    protected void rollback() throws Exception {

    }

    @Override
    public void updateForBeforeDao() throws Exception {
        logger.info("强制改成活跃捞取用户");
        FlywayMongoCollection roleServerInfoCollection = getMongoCollection(RoleServerInfo.class);
        UpdateResult result = roleServerInfoCollection.updateMany(new Document(), Updates.set("active", true));
        logger.info("强制改成活跃捞取用户完成: {}", result.getModifiedCount());
    }

}
