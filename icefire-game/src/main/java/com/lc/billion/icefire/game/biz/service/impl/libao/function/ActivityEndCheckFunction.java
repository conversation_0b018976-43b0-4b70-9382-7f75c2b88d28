package com.lc.billion.icefire.game.biz.service.impl.libao.function;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.biz.model.libao.CheckContext;
import com.lc.billion.icefire.game.biz.model.libao.LiBaoFunction;
import com.lc.billion.icefire.game.biz.model.libao.LiBaoFunctionType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

/**
 * by <EMAIL> 2024-04-07 09:50:51
 *
 */
@Service
public class ActivityEndCheckFunction implements LiBaoConditionCheck {
	private static final Logger logger = LoggerFactory.getLogger(ActivityEndCheckFunction.class);

    @Autowired
    private ActivityBeginCheckFunction activityBeginCheckFunction;

	@Override
	public LiBaoFunctionType getType() {
		return LiBaoFunctionType.ACTIVITY_END;
	}

	@Override
	public boolean check(Role role, LiBaoFunction func, CheckContext context) {
        var cp = func.getCp();
        String activityMetaId = cp.get(0);
        int hours = cp.getInt(1);

        ArrayList<Long> activityTime = activityBeginCheckFunction.getActivityTime(role, activityMetaId);
        if (activityTime.size() != 2) {
            return false;
        }

        long activityEndTime = activityTime.get(1);
        long time = activityEndTime - hours * TimeUtil.HOUR_MILLIS;
        context.setTime(time);

        return true;
	}
}
