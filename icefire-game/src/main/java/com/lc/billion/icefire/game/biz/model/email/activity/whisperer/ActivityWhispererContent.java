package com.lc.billion.icefire.game.biz.model.email.activity.whisperer;

import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.game.biz.model.role.info.RoleInfo;
import com.lc.billion.icefire.protocol.structure.PsActivityEmail;
import com.simfun.sgf.utils.JavaUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 低语者救赎活动，集结攻击个人阶段怪物，通知邮件内容
 *
 * <AUTHOR>
 * @date 2020/10/12
 */
public class ActivityWhispererContent {

	private List<HelperDetail> helperList;
	private int x;// 怪物坐标x
	private int y;// 怪物坐标y

	public ActivityWhispererContent() {

	}

	public List<HelperDetail> getHelperList() {
		return helperList;
	}

	public void setHelperList(List<HelperDetail> helperList) {
		this.helperList = helperList;
	}

	public int getX() {
		return x;
	}

	public void setX(int x) {
		this.x = x;
	}

	public int getY() {
		return y;
	}

	public void setY(int y) {
		this.y = y;
	}

	public void wrapperInfo(PsActivityEmail info) {
		info.setX(x);
		info.setY(y);
		if (JavaUtils.bool(helperList))
			for (HelperDetail detail : helperList)
				info.addToWhisperernfo(detail.toInfo());

	}

	public void addHelper(String name, String head, RoleInfo roleInfo, String alias, List<SimpleItem> rewards) {
		if (this.helperList == null)
			this.helperList = new ArrayList<>();
		this.helperList.add(new HelperDetail(name, head, roleInfo, alias, rewards));
	}
}
