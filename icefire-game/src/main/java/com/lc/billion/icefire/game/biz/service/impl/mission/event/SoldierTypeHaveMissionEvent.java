package com.lc.billion.icefire.game.biz.service.impl.mission.event;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.config.MissionConfig.MissionMeta;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.ArmyDao;
import com.lc.billion.icefire.game.biz.manager.ArmyManager;
import com.lc.billion.icefire.game.biz.manager.SoldierManager;
import com.lc.billion.icefire.game.biz.model.army.ArmyInfo;
import com.lc.billion.icefire.game.biz.model.mission.IMissionItem;
import com.lc.billion.icefire.game.biz.model.mission.AbstractBaseMissionItem;
import com.lc.billion.icefire.game.biz.model.mission.MissionType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.soldier.Soldier;

import java.util.Collection;
import java.util.List;

public class SoldierTypeHaveMissionEvent extends AbstractMissionEvent {

	@Override
	public MissionType getType() {
		return MissionType.SOLDIER_TYPE_HAVE;
	}

	@Override
	public void start(Role role, IMissionItem item, MissionMeta meta) {
		long amount = getRealProgress(role, meta.getPara1Int());
		setAmount(item, meta, amount, true);
	}

	@Override
	public long getRealProgress(Role role, int param) {
		int result = 0;
		SoldierManager soldierManager = Application.getBean(SoldierManager.class);
		for (Soldier soldier : soldierManager.getAllSoldier(role).values()) {
			if (param == -1 || soldier.getMeta().getType().getValue() == param) {
				result += soldier.getCount();
			}
		}
		ArmyDao armyDao = Application.getBean(ArmyDao.class);
		Collection<ArmyInfo> armys = armyDao.findByRoleId(role.getPersistKey());
		if (armys != null) {
			ArmyManager armyManager = Application.getBean(ArmyManager.class);
			for (ArmyInfo army : armys) {
				List<Soldier> soldierList = armyManager.getSolders(army);
				for (Soldier soldier : soldierList) {
					if (param == -1 || soldier.getMeta().getType().getValue() == param) {
						result += soldier.getCount();
					}
				}
			}
		}
		return result;
	}

	@Override
	public boolean effect(IMissionItem item, MissionMeta meta, Object... params) {
		Role role = (Role) params[0];
		long typeNum = getRealProgress(role, meta.getPara1Int());
		if (typeNum == item.getProgress()) {// 新数量小于进程不更新, 不影响任务
			return false;
		}
		setAmount(item, meta, typeNum, true);
		return true;
	}

}
