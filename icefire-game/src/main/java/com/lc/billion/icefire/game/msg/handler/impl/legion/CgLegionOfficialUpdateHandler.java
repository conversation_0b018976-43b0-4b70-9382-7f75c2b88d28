package com.lc.billion.icefire.game.msg.handler.impl.legion;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.kvkseason.biz.service.impl.legion.LegionService;
import com.lc.billion.icefire.protocol.CgLegionOfficialUpdate;

/**
 * <AUTHOR>
 *
 */
@Controller
public class CgLegionOfficialUpdateHandler extends CgAbstractMessageHandler<CgLegionOfficialUpdate> {

	@Autowired
	private LegionService legionService;

	@Override
	protected void handle(Role role, CgLegionOfficialUpdate message) {
		legionService.cgLegionOfficialUpdate(role, message.getGroup(), message.getOrder(), message.getRoleId());
	}

}
