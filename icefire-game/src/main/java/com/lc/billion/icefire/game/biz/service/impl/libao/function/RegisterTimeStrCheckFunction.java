package com.lc.billion.icefire.game.biz.service.impl.libao.function;

import com.lc.billion.icefire.game.biz.model.libao.LiBaoFunction;
import com.lc.billion.icefire.game.biz.model.libao.LiBaoFunctionType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.ExpectedException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;

/**
 * 
 * <AUTHOR>
 *
 */
@Service
public class RegisterTimeStrCheckFunction implements LiBaoConditionCheck {

	private static final Logger logger = LoggerFactory.getLogger(RegisterTimeStrCheckFunction.class);

	@Override
	public LiBaoFunctionType getType() {
		return LiBaoFunctionType.REGISTER_TIME_STR;
	}

	@Override
	public boolean check(Role role, LiBaoFunction func) {
		var cp = func.getCp();
		String param1 = cp.get(0);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		long cfgTime = 0L;
		try {
			cfgTime = sdf.parse(param1).getTime();
		}catch (ExpectedException ignored) {

		} catch (Exception e) {
			ErrorLogUtil.exceptionLog("RegisterTimeStrCheckFunction CaughtException", e);
		}
		if (role.getCreateTime() >= cfgTime) {
			return true;
		}
		return false;
	}

}
