package com.lc.billion.icefire.game.biz.dao;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.async.BindSaveThreadOperation;
import com.lc.billion.icefire.game.biz.async.ImmediatelyFlushBindSaveThreadOperation;
import com.lc.billion.icefire.game.biz.model.AbstractEntity;
import com.lc.billion.icefire.game.biz.model.IRolesEntity;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.model.role.RoleServerInfo;
import com.lc.billion.icefire.game.biz.service.impl.DaoService;
import com.lc.billion.icefire.game.biz.service.impl.bilog.BiLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.log.error.ErrorLogUtil;
import com.lc.billion.icefire.game.exception.AlertException;
import com.lc.billion.icefire.game.exception.ExpectedException;
import com.simfun.sgf.utils.JavaUtils;
import org.jongo.MongoCursor;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;

public abstract class RolesEntityDao<ENTITY_TYPE extends AbstractEntity & IRolesEntity> extends AbstractMemoryDao<ENTITY_TYPE> {

	@Autowired(required = false)
	private BiLogUtil biLogUtil;
	/**
	 * 这个结构用来防止玩家迁服失败数据丢失后没办法处理 等永久迁服稳定后 可以干掉 只加不减
	 */
	private Multimap<Long, ENTITY_TYPE> migratedEnties = ArrayListMultimap.create();

	protected RolesEntityDao(Class<ENTITY_TYPE> clazz, boolean needIdGenerator) {
		super(clazz, needIdGenerator);
	}

	/**
	 * 加载所有活跃的（与Role相关联的）子实体到JVM里。（一般在服务器启动时调用） 。
	 */
	public void loadAll(Map<Integer, List<Long>> dbWithRoleIds, Map<Long, RoleServerInfo> roleIdToRoleServerInfos) {
		if (loadAll) {
			// 重复执行
			throw new AlertException("loadAll方法重复执行","className",this.getClass().getSimpleName());
		}
		long start = TimeUtil.getNow();
		int sizeSum = 0;
		for (Map.Entry<Integer, List<Long>> entry : dbWithRoleIds.entrySet()) {
			long s = TimeUtil.getNow();
			int db = entry.getKey();
			List<Long> roleIds = entry.getValue();
			Collection<ENTITY_TYPE> dbFindAll = transform(doFindAll(db, roleIds));
			int size = dbFindAll.size();
			sizeSum += size;
			log.info("{}从数据库 {}加载活跃的实体{}个,用时{}", this.getClass().getSimpleName(), db, size, TimeUtil.getNow() - s);
			s = TimeUtil.getNow();
			for (ENTITY_TYPE entity : dbFindAll) {
				entity.setDB(db); // 只是 @JsonIgnore
				RoleServerInfo roleServerInfo = roleIdToRoleServerInfos.get(entity.getRoleId());
				// if (this.getClass().getSimpleName().equals("RoleDao")) {
				// log.error("serverId测试roleId={},serverId={}", entity.getRoleId(), serverId);
				// }
				entity.setCurrentServerId(roleServerInfo.getCurrentServerId());
				entity.setoServerId(roleServerInfo.getRegisterServerId());
				putMemory(entity, false); // 启动服务器时不抛异常
			}
			log.info("{}把数据库放入内存,用时{}", this.getClass().getSimpleName(), TimeUtil.getNow() - s);
		}
		loadAll = true;
		log.info("{} 共加载活跃的实体 {} 个，消耗 {} 毫秒", this.getClass().getSimpleName(), sizeSum, TimeUtil.getNow() - start);
	}

	/**
	 * 子类需要实现此方法。从数据库查找所有活跃的Entity
	 * 
	 *            有的findAll需要,有的findAll不需要,具体看业务
	 * @return
	 */
	protected abstract MongoCursor<ENTITY_TYPE> doFindAll(int db, List<Long> roleIds);

	/**
	 * 加载某个玩家相关实体到JVM里。（当7天未登录玩家，被清除内存后，玩家重新登录，调用这个方法）
	 */
	public void loadOne(int db, Long playerId, RoleServerInfo roleServerInfo) {
		if (null == playerId) {
			throw new NullPointerException("playerId不能为null");
		}
		// ServerType currentGameServerType = configCenter.getCurrentGameServerType();
		Collection<ENTITY_TYPE> entityCollection = transform(doFindByPlayerId(db, playerId));
		if (entityCollection != null) {
			for (ENTITY_TYPE entity : entityCollection) {
				// // 特殊情况
				// // 例如注册服1，原服2，k服80000，G服85000，RoleServerInfo为当前服信息
				// // 各种情况分析
				// switch (currentGameServerType) {
				// case GAME:
				// // 1、原始服load，db为注册服1，oServerId为2，currentServerId为2
				// entity.setDB(db);
				// entity.setoServerId(roleServerInfo.getMigrateServerId());
				// entity.setCurrentServerId(roleServerInfo.getMigrateServerId());
				// break;
				// case GVG_BATTLE:
				// // 2、从原始服到GVG服load，db为注册服1，oServerId为2，currentServerId为85000
				// entity.setDB(db);
				// entity.setoServerId(roleServerInfo.getMigrateServerId());
				// entity.setCurrentServerId(roleServerInfo.getMigrateServerId());
				// break;
				// default:
				entity.setDB(db);
				entity.setCurrentServerId(roleServerInfo.getMigrateServerId());
				entity.setoServerId(roleServerInfo.getRegisterServerId());
				putMemory(entity, false); // 捞一个玩家时也不抛异常
			}
			log.info("{}从服务器{}加载 {} 个", this.getClass().getSimpleName(), db, entityCollection.size());
		}
	}

	/**
	 * 子类需要实现此方法。根据playerId 从数据库查找相关实体
	 * 
	 * @return
	 */
	protected abstract MongoCursor<ENTITY_TYPE> doFindByPlayerId(int db, Long roleId);

	//
	// clear
	//

	/**
	 * 从内存里清理掉长时间不登陆的玩家相关的数据。
	 * 
	 * @param playerId
	 */
	/**
	 * 
	 * @param playerId
	 * @param forMoveToOtherServer
	 *            用来区分是否使用迁服专用线程池。
	 * @param onFinished
	 *            保存完毕后 调用
	 * @return
	 */
	public boolean clear(Long playerId, boolean forMoveToOtherServer, Runnable onFinished, boolean isSynchronize) {
		return doClear(playerId, forMoveToOtherServer, onFinished, isSynchronize);
	}

	public boolean batchClear(String db, Long modIndex, Map<Long, Boolean> playerMap, boolean isSynchronize) {
		return doBatchClear(db, modIndex, playerMap, isSynchronize);
	}

	// public void clear4MoveToOtherServer(Long playerId) {
	// doClear(playerId, true); // 迁服需要从当前服内存中移除，需要立即落地。
	// }

	private boolean doClear(Long playerId, boolean forMoveToOtherServer, Runnable onFinished, boolean isSynchronize) {
		try {
			Collection<Long> entityIds = clearMemoryIndexes(playerId);
			/**
			 * 20200814，迁服测试时发现，旧服扣道具但是没落地，去新服登陆后，道具依然存在。 具体原因：
			 * 扣道具会引起delete，然而，delete后，clearMemoryIndexes(playerId)方法返回的道具实体ID集合里面，
			 * 就不存在这个被delete的道具Id了（当然此时在modifiedEntities里面有此ID对应的delete）此时就会导致
			 * flushImmediately不到这个delete的道具Id。所以导致扣道具数据没及时落地，玩家就在新服登陆了。。。
			 */
			/**
			 * 为解决上面20200814说的问题，这里针对所有modifiedEntities先做一次过滤，找出跟此player关联的delete的实体Id
			 */
			Collection<Long> deleteIds = new ArrayList<>();
			this.modifiedEntities.forEach(new BiConsumer<Long, BeModified>() {

				@Override
				public void accept(Long k, AbstractMemoryDao<ENTITY_TYPE>.BeModified v) {
					if (v.getOperator() == Modify.DELETE && v.getBeModified() instanceof IRolesEntity) {
						IRolesEntity are = (IRolesEntity) v.getBeModified();
						if (are.getRoleId() != null && are.getRoleId().equals(playerId)) {
							deleteIds.add(k);
						}
					}
				}
			});
			/**
			 * delete的和save的合并到一起。
			 */
			entityIds = entityIds == null ? new ArrayList<>() : entityIds;
			deleteIds.addAll(entityIds);

			/**
			 * 及时落地
			 */
			if (forMoveToOtherServer) {
				if (isSynchronize) {
					flushImmediatelyAndRmoveCache(deleteIds, onFinished);
				} else {
					// 迁服：考虑专门的存储线程进行处理，不放在 asyncOperationService 里，防止任务过多，无法及时落地。
					asyncOperationService.execute(new ImmediatelyFlushBindSaveThreadOperation(playerId) {
						@Override
						protected void save() {
							flushImmediatelyAndRmoveCache(deleteIds, onFinished);
						}
					});
				}
			} else {
				if (isSynchronize) {
					flushImmediatelyAndRmoveCache(deleteIds, onFinished);
				} else {
					// 让SaveLoop立即执行。
					asyncOperationService.execute(new BindSaveThreadOperation(playerId) {
						@Override
						protected void save() {
							flushImmediatelyAndRmoveCache(deleteIds, onFinished);
						}
					});
				}
			}

			return true;
		} catch (RuntimeException e) {
			// clear 方法非常特殊，如果某个模块的clear出错，会导致 某玩家数据清理了一半，从而影响后续玩家登录。
			// 因此，这里必须确保出现异常也不中断后续执行（即吃掉异常）。
			ErrorLogUtil.exceptionLog("玩家清理失败", e, "className", this.getClass().getSimpleName(), "playerId", playerId);
			return false;
		}
	}

	/**
	 * 批量清理玩家数据
	 *
	 * @param db            数据库，所有玩家必须在同一个数据库
	 * @param modIndex      玩家id和线程数的取余，确保清理玩家数据被同一个线程处理
	 * @param playerMap     玩家id集合，所有玩家id与线程数的取余都必须为modIndex
	 * @param isSynchronize 是否同步
	 * @return 是否成功
	 */
	private boolean doBatchClear(String db, Long modIndex, Map<Long, Boolean> playerMap, boolean isSynchronize) {
		try {
			List<Long> deleteIds = new ArrayList<>();
			for (Long playerId : playerMap.keySet()) {
				Collection<Long> indexes = clearMemoryIndexes(playerId);
				if (indexes != null) {
					deleteIds.addAll(indexes);
				}
			}
			/**
			 * 20200814，迁服测试时发现，旧服扣道具但是没落地，去新服登陆后，道具依然存在。 具体原因：
			 * 扣道具会引起delete，然而，delete后，clearMemoryIndexes(playerId)方法返回的道具实体ID集合里面，
			 * 就不存在这个被delete的道具Id了（当然此时在modifiedEntities里面有此ID对应的delete）此时就会导致
			 * flushImmediately不到这个delete的道具Id。所以导致扣道具数据没及时落地，玩家就在新服登陆了。。。
			 */
			/**
			 * 为解决上面20200814说的问题，这里针对所有modifiedEntities先做一次过滤，找出跟此player关联的delete的实体Id
			 */
			this.modifiedEntities.forEach((k, v) -> {
				if (v.getOperator() == Modify.DELETE && v.getBeModified() instanceof IRolesEntity) {
					IRolesEntity are = (IRolesEntity) v.getBeModified();
					if (are.getRoleId() != null && playerMap.containsKey(are.getRoleId())) {
						deleteIds.add(k);
					}
				}
			});
			if (deleteIds.isEmpty()) {
				return true;
			}
			// 及时落地
			if (isSynchronize) {
				flushBatchImmediatelyAndRemoveCache(db, deleteIds);
			} else {
				asyncOperationService.execute(new BindSaveThreadOperation(modIndex) {
					@Override
					protected void save() {
						flushBatchImmediatelyAndRemoveCache(db, deleteIds);
					}
				});
			}
			return true;
		} catch (RuntimeException e) {
			// clear 方法非常特殊，如果某个模块的clear出错，会导致 某玩家数据清理了一半，从而影响后续玩家登录。
			// 因此，这里必须确保出现异常也不中断后续执行（即吃掉异常）。
			ErrorLogUtil.exceptionLog("批量清理玩家失败", e, "className", this.getClass().getSimpleName(), "playerIds", playerMap.keySet());
			return false;
		}
	}

	private void flushImmediatelyAndRmoveCache(Collection<Long> deleteIds, Runnable onFinished) {
		for (Long entityId : deleteIds) {
			ENTITY_TYPE entity = RolesEntityDao.this.entities.remove(entityId);
			// 需要判空，因为有的实体不存在，比如玩家没有联盟，AllianceMember就是空
			if (entity != null) {
				bizKeyMap.remove(entity);
			}
			flushImmediately(entityId);
		}
		if (null != onFinished) {
			onFinished.run();
		}
	}

	private void flushBatchImmediatelyAndRemoveCache(String db, List<Long> deleteIds) {
		for (Long entityId : deleteIds) {
			ENTITY_TYPE entity = RolesEntityDao.this.entities.remove(entityId);
			// 需要判空，因为有的实体不存在，比如玩家没有联盟，AllianceMember就是空
			if (entity != null) {
				bizKeyMap.remove(entity);
			}
		}
		flushBatchImmediately(db, deleteIds);
	}

	/**
	 * 为了从内存里清理不活跃玩家 子类应覆盖此方法。从Dao里面按playerID查询的数据结构中删除该玩家对应的所有实体。
	 * 注意：这几年的实践证明，此方法有些令人费解，极易出错： 1）首先，只有player的子实体（如：hero、item等）的Dao，需要仔细实现此方法。
	 * 其他的与player无关的实体的Dao不需要特殊逻辑，可以直接返回空集合/null即可。 即：doFindByPlayerId
	 * 有业务逻辑的，一定要在clearMemoryIndexes里也有对应清理逻辑。 2）其次，需要实现此方法的Dao，要注意这个方法的参数是playerId。
	 * player相关的子实体比如hero对应的HeroDao里面，通常会有按playerId保存的Hero实体集合的Map，我们首先是要把这个玩家对应的所有Hero的集合从这个Map里remove掉。
	 * 3）最后，要注意的是，此方法的返回值，是上面第2步里remove掉的所有实体的id，对于HeroDao来说，就是这个playerId对应的所有Hero的Id集合。
	 * 只有正确返回了playerID对应的所有Hero的ID，AbstractMemoryDao才知道需要从自身的entities集合里移除掉哪些Hero。
	 * 如果不返回HeroID，那么AbstractMemoryDao就不会从内存中移除对应Hero，那么下次该玩家登录时引发loadOne从数据库里加载Hero并放入entities集合，
	 * 就会报错：IllegalArgumentException XXXX 对应的实体已经存在！
	 * 
	 * @param
	 */
	protected abstract Collection<Long> clearMemoryIndexes(Long playerId);

	public abstract Collection<ENTITY_TYPE> findByRoleId(Long roleId);

	protected ENTITY_TYPE createEntity(Role role, ENTITY_TYPE entity) {
		if (role.isClearing()) {
			ErrorLogUtil.errorLog("玩家清理中,创建失败","className",this.getClass().getSimpleName(),
                    "roleId",role.getPersistKey());
			return null;
		}
		return super.createEntity(role.getDB(), role.getoServerId(), role.getCurrentServerId(), entity);
	}

	protected ENTITY_TYPE createEntity(Role role) {
		if (role.isClearing()) {
            ErrorLogUtil.errorLog("玩家清理中,创建失败","className",this.getClass().getSimpleName(),
                    "roleId",role.getPersistKey());
			return null;
		}
		return super.createById(role.getDB(), role.getoServerId(), role.getCurrentServerId(), role.getId());
	}

	/**
	 * 注意：这个方法任何人不要使用，专为永久迁服做的数据落地操作 <br>
	 */
	public void saveForMigrateEver(int srcDB, int targetDB, Long roleId) {
		if (null == roleId) {
			throw new NullPointerException("roleId不能为null");
		}
		Collection<ENTITY_TYPE> findAll = findByRoleId(roleId);
		if (findAll != null) {
			log.info("永久迁服数据落地实体{}从{}到{}", this.getClass().getSimpleName(), srcDB, targetDB);
			for (ENTITY_TYPE entity : findAll) {
				if (entity == null) {
					continue;
				}
				dbSave(targetDB, entity);
//				migratedEnties.put(entity.getRoleId(), entity);
			}
			log.info("永久迁服数据落地实体{}从{}到{},共{}个", this.getClass().getSimpleName(), srcDB, targetDB, findAll.size());
		} else {
			log.info("永久迁服数据落地数据库{}的{}没有数据", srcDB, this.getClass().getSimpleName());
		}
	}

	public boolean flush(Long playerId, boolean forMoveToOtherServer, Runnable onFinished, boolean isSynchronize) {
		try {
			/**
			 * 20200814，迁服测试时发现，旧服扣道具但是没落地，去新服登陆后，道具依然存在。 具体原因：
			 * 扣道具会引起delete，然而，delete后，clearMemoryIndexes(playerId)方法返回的道具实体ID集合里面，
			 * 就不存在这个被delete的道具Id了（当然此时在modifiedEntities里面有此ID对应的delete）此时就会导致
			 * flushImmediately不到这个delete的道具Id。所以导致扣道具数据没及时落地，玩家就在新服登陆了。。。
			 */
			/**
			 * 为解决上面20200814说的问题，这里针对所有modifiedEntities先做一次过滤，找出跟此player关联的delete的实体Id
			 */
			Collection<Long> deleteIds = new ArrayList<>();
			this.modifiedEntities.forEach(new BiConsumer<Long, BeModified>() {

				@Override
				public void accept(Long k, AbstractMemoryDao<ENTITY_TYPE>.BeModified v) {
					if (v.getOperator() == Modify.DELETE && v.getBeModified() instanceof IRolesEntity) {
						IRolesEntity are = (IRolesEntity) v.getBeModified();
						if (are.getRoleId() != null && are.getRoleId().equals(playerId)) {
							deleteIds.add(k);
						}
					}
				}
			});
			Collection<ENTITY_TYPE> findByRoleId = findByRoleId(playerId);
			if (findByRoleId == null) {
				ErrorLogUtil.errorLog("RolesEntityDaofindByRoleId方法返回null","className",getClass().getSimpleName());
				return false;
			}
			if (JavaUtils.bool(findByRoleId)) {
				for (ENTITY_TYPE entity : findByRoleId) {
					deleteIds.add(entity.getPersistKey());
				}
			}
			/**
			 * 及时落地
			 */
			if (forMoveToOtherServer) {
				if (isSynchronize) {
					flushImmediatelyAndRmoveCache(deleteIds, onFinished);
				} else {
					// 迁服：考虑专门的存储线程进行处理，不放在 asyncOperationService 里，防止任务过多，无法及时落地。
					asyncOperationService.execute(new ImmediatelyFlushBindSaveThreadOperation(playerId) {
						@Override
						protected void save() {
							flushImmediatelyAndRmoveCache(deleteIds, onFinished);
						}
					});
				}
			} else {
				if (isSynchronize) {
					flushImmediatelyAndRmoveCache(deleteIds, onFinished);
				} else {
					// 让SaveLoop立即执行。
					asyncOperationService.execute(new BindSaveThreadOperation(playerId) {
						@Override
						protected void save() {
							flushImmediatelyAndRmoveCache(deleteIds, onFinished);
						}
					});
				}
			}

			return true;
		} catch (RuntimeException e) {
			// clear 方法非常特殊，如果某个模块的clear出错，会导致 某玩家数据清理了一半，从而影响后续玩家登录。
			// 因此，这里必须确保出现异常也不中断后续执行（即吃掉异常）。
			ErrorLogUtil.errorLog("玩家清理失败","className",this.getClass().getSimpleName(),"playerId",playerId);
			return false;
		}
	}

	public boolean doDelete(Long playerId) {
		try {
			Collection<Long> entityIds = clearMemoryIndexes(playerId);
			if (entityIds == null) {
				ErrorLogUtil.errorLog("RolesEntityDaoclearMemoryIndexes方法返回null","className",getClass().getSimpleName());
				return false;
			}
			if (JavaUtils.bool(entityIds)) {
				for (Long id : entityIds) {
					ENTITY_TYPE entity_TYPE = findById(id);
					if (entity_TYPE != null) {
						biLogUtil.migrateEverDeleteEntity(playerId, entity_TYPE.toString());
					}
					delete(id);
				}
			}
			return true;
		} catch (RuntimeException e) {
			// clear 方法非常特殊，如果某个模块的clear出错，会导致 某玩家数据清理了一半，从而影响后续玩家登录。
			// 因此，这里必须确保出现异常也不中断后续执行（即吃掉异常）。
            ErrorLogUtil.errorLog("玩家清理失败", e,"className",this.getClass().getSimpleName(),"playerId",playerId);
			return false;
		}
	}

	public void clearAll() {
		log.info("clearAll {}", this.getClass().getSimpleName());
		log.info("entities.size={}, bizKeyMap.size={}, modifiedEntities.size={}", entities.size(), bizKeyMap.size(), modifiedEntities.size());
		entities.clear();
		bizKeyMap.clear();
		modifiedEntities.clear();
		Application.getAllServerIds().forEach(serverId -> drop(serverId));
	}

	public void changeRoleEntityCurrentServerId(int oldCurrentServerId, int newCurrentServerId, Long roleId) {
		if (null == roleId) {
			throw new NullPointerException("roleId不能为null");
		}
		Collection<ENTITY_TYPE> findAll = findByRoleId(roleId);
		if (findAll != null) {
			log.info("changeRoleEntityCurrentServerId roleId={} {}从{}到{}", this.getClass().getSimpleName(), roleId, oldCurrentServerId, newCurrentServerId);
			for (ENTITY_TYPE entity : findAll) {
				if (entity == null) {
					continue;
				}
				entity.setCurrentServerId(newCurrentServerId);
			}
			log.info("changeRoleEntityCurrentServerId roleId={} {}从{}到{} 记录={}", this.getClass().getSimpleName(), roleId, oldCurrentServerId, newCurrentServerId, findAll.size());
		} else {
			log.warn("changeRoleEntityCurrentServerId norecord roleId={} {}从{}到{}", roleId, this.getClass().getSimpleName(), oldCurrentServerId, newCurrentServerId);
		}
	}

	public boolean copy(Role role, Long sourceRoleId, Long targetRoleId) {
		return copy(role, sourceRoleId, targetRoleId, null);
	}


	protected boolean copy(Role role, Long sourceRoleId, Long targetRoleId, String sourceEntityJson) {
		try {
			Collection<ENTITY_TYPE> allEntities = findByRoleId(sourceRoleId);
			// 基类只能处理_id为roleId的数据结构
			if (allEntities != null && allEntities.size() > 1) {
				ErrorLogUtil.errorLog("复制失败,此表不是roleId为主键,role存在多个实体,需要单独实现copy接口", "collectionName",getCollectionName());
				return false;
			}
			if (allEntities != null && !allEntities.isEmpty()) {
				doDelete(targetRoleId);
			}
			if (sourceEntityJson == null) {
				ENTITY_TYPE sourceEntity = findById(sourceRoleId);
				if (sourceEntity == null) {
					ErrorLogUtil.errorLog("复制失败,没有找到源数据", "collectionName", getCollectionName());
					return false;
				}
				sourceEntityJson = JSON.toJSONString(sourceEntity);
			}
			if (sourceEntityJson == null || sourceEntityJson.isEmpty()) {
				ErrorLogUtil.errorLog("复制失败,没有找到源数据", "collectionName",getCollectionName());
				return false;
			}
			// TODO 如果需要可以将源数据出到json文件

			ENTITY_TYPE targetEntity = (ENTITY_TYPE) JSON.parseObject(sourceEntityJson, getEntityClass());
			targetEntity.setRoleId(targetRoleId);
			targetEntity.setPersistKey(targetRoleId);
			createEntity(role, targetEntity);
			return true;
		} catch (Exception e) {
			if (!(e instanceof ExpectedException)) {
				ErrorLogUtil.exceptionLog("复制失败", e, "collectionName", getCollectionName());
			}
			return false;
		}
	}

	/**
	 * 复制简单的数据结构，需要去除_id, roleId, playerId属性
	 *
	 * @param sourceEntity
	 * @param targetEntity
	 */
	protected void copySimpleProperties(ENTITY_TYPE sourceEntity, ENTITY_TYPE targetEntity) {
		if (sourceEntity == null || targetEntity == null) {
			return;
		}
		Class<?> clazz = sourceEntity.getClass();
		for (Field field : clazz.getDeclaredFields()) {
			boolean oldAccessible = field.isAccessible();
			field.setAccessible(true);
			Class<?> fieldType = field.getType();
			if (isPrimitiveOrWrapper(fieldType)) {
				// 特殊的值 roleId等需要过滤
				if ("id".equals(field.getName()) || "_id".equals(field.getName()) || "roleId".equals(field.getName()) || "playerId".equals(field.getName())) {
					continue;
				}
				// 日志的需要去掉
				if (field.getName().contains("log") || "serialVersionUID".equals(field.getName())) {
					continue;
				}
				try {
					field.set(targetEntity, field.get(sourceEntity));
				} catch (IllegalAccessException e) {
					throw new RuntimeException(e);
				} finally {
					field.setAccessible(oldAccessible);
				}
			} else if (Map.class.isAssignableFrom(fieldType)) {
				Type genericType = field.getGenericType();
				if (genericType instanceof ParameterizedType) {
					ParameterizedType parameterizedType = (ParameterizedType) genericType;
					Type[] typeArguments = parameterizedType.getActualTypeArguments();
					// 获取键值类型
					Class<?> keyType = (Class<?>) typeArguments[0];
					Class<?> valueType = (Class<?>) typeArguments[1];
					if (!isPrimitiveOrWrapper(keyType) || !isPrimitiveOrWrapper(valueType)) {
						continue;
					}
					// map都是基础键值，可以直接赋值
					try {
						if (field.get(targetEntity) == null) {
							field.set(targetEntity, new HashMap<>());
							continue;
						}
						Map<Object, Object> fromMap = (Map<Object, Object>) field.get(sourceEntity);
						if (fromMap != null) {
							Map<Object, Object> toMap = (Map<Object, Object>) field.get(targetEntity);
							toMap.clear();
							toMap.putAll(fromMap);
						}
					} catch (IllegalAccessException e) {
						throw new RuntimeException(e);
					}

				}
			} else if (List.class.isAssignableFrom(fieldType)) {
				Type genericType = field.getGenericType();
				if (genericType instanceof ParameterizedType) {
					ParameterizedType parameterizedType = (ParameterizedType) genericType;
					Type[] typeArguments = parameterizedType.getActualTypeArguments();
					// 获取键值类型
					Class<?> keyType = (Class<?>) typeArguments[0];
					if (!isPrimitiveOrWrapper(keyType)) {
						continue;
					}
					// LIst基础数据类型的话，可以直接赋值
					try {
						if (field.get(targetEntity) == null) {
							field.set(targetEntity, new ArrayList<>());
							continue;
						}
						List<Object> fromList = (List<Object>) field.get(sourceEntity);
						if (fromList != null) {
							List<Object> toList = (List<Object>) field.get(targetEntity);
							toList.clear();
							toList.addAll(fromList);
						}
					} catch (IllegalAccessException e) {
						throw new RuntimeException(e);
					}

				}
			} else if (Set.class.isAssignableFrom(fieldType)) {
				Type genericType = field.getGenericType();
				if (genericType instanceof ParameterizedType) {
					ParameterizedType parameterizedType = (ParameterizedType) genericType;
					Type[] typeArguments = parameterizedType.getActualTypeArguments();
					// 获取键值类型
					Class<?> keyType = (Class<?>) typeArguments[0];
					if (!isPrimitiveOrWrapper(keyType)) {
						continue;
					}
					// Set基础数据类型的话，可以直接赋值
					try {
						if (field.get(targetEntity) == null) {
							field.set(targetEntity, new HashSet<>());
							continue;
						}
						Set<Object> fromList = (Set<Object>) field.get(sourceEntity);
						if (fromList != null) {
							Set<Object> toList = (Set<Object>) field.get(targetEntity);
							toList.clear();
							toList.addAll(fromList);
						}
					} catch (IllegalAccessException e) {
						throw new RuntimeException(e);
					}

				}
			} else if (fieldType.isArray()) {
				Class<?> componentType = fieldType.getComponentType();
				if (isPrimitiveOrWrapper(componentType) || componentType.equals(String.class)) {
					try {
						Object fromArray = field.get(sourceEntity);
						if (fromArray != null) {
							int length = java.lang.reflect.Array.getLength(fromArray);
							Object toArray = java.lang.reflect.Array.newInstance(componentType, length);
							System.arraycopy(fromArray, 0, toArray, 0, length);
							field.set(targetEntity, toArray);
						}
					} catch (IllegalAccessException e) {
						throw new RuntimeException(e);
					}
				}
			}
		}
	}


	private static boolean isPrimitiveOrWrapper(Class<?> clazz) {
		return clazz.isPrimitive() ||
				clazz == Boolean.class ||
				clazz == Byte.class ||
				clazz == Short.class ||
				clazz == Integer.class ||
				clazz == Long.class ||
				clazz == Float.class ||
				clazz == Double.class ||
				clazz == Character.class ||
				clazz == String.class ||
				clazz == AtomicInteger.class;
	}
	public void fixData(int db, Long roleId) {
		List<AbstractEntity> list = new ArrayList<>();
        try (var iter = doFindByPlayerId(db, roleId)) {
            while (iter.hasNext()) {
                var entity = iter.next();
                if (list.contains(entity)) {
                    var old = list.get(list.indexOf(entity));
                    if (old.getCreateTime() < entity.getCreateTime()) {
                        deleteFromDB(db, old.getPersistKey());
                        log.warn("role={} db={} entity:{} 数据重复 old={} repeated={}", roleId, db, entity.getClass().getSimpleName(), JSON.toJSON(old), JSON.toJSON(entity));
                    } else {
                        deleteFromDB(db, entity.getPersistKey());
                        log.warn("role={} db={} entity:{} 数据重复 old={} repeated={}", roleId, db, entity.getClass().getSimpleName(), JSON.toJSON(entity), JSON.toJSON(old));
                    }
                    list.add(entity);
                } else {
                    list.add(entity);
                }
            }
        } catch (IOException e) {
            ErrorLogUtil.errorLog("修复数据失败", e, "roleId", roleId, "db", db, "entityName", getCollectionName());
        }
    }
}
