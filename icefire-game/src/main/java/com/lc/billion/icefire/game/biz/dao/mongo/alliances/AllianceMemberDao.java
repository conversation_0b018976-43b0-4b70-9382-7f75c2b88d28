package com.lc.billion.icefire.game.biz.dao.mongo.alliances;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.core.utils.MyConcurrentMap;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.dao.AlliancesEntityDao;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.model.alliance.AllianceMember;
import com.lc.billion.icefire.game.biz.model.role.Role;
import org.jongo.MongoCursor;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * <AUTHOR>
 *
 */
@Repository
public class AllianceMemberDao extends AlliancesEntityDao<AllianceMember> {

	private ConcurrentMap<Long, List<AllianceMember>> allianceMemberByAllianceId = new MyConcurrentMap<>();

	protected AllianceMemberDao() {
		super(AllianceMember.class, false);
	}

	@Override
	protected MongoCursor<AllianceMember> doFindAll(int db, List<Long> playerIds) {
		return dbFindByAllianceIds(db, playerIds);
	}

	@Override
	protected void putMemoryIndexes(AllianceMember entity) {
		allianceMemberByAllianceId.compute(entity.getAllianceId(), (k, v) -> v == null ? new CopyOnWriteArrayList<>() : v).add(entity);
	}

	@Override
	protected void removeMemoryIndexes(AllianceMember entity) {
		allianceMemberByAllianceId.compute(entity.getAllianceId(), (k, v) -> v == null ? new CopyOnWriteArrayList<>() : v).remove(entity);
	}

	public List<AllianceMember> findByAllianceId(Long allianceId) {
		return allianceMemberByAllianceId.getOrDefault(allianceId, List.of());
	}

	public int findMemberNumByAllianceId(long allianceId) {
		return allianceMemberByAllianceId.getOrDefault(allianceId, List.of()).size();
	}

	public AllianceMember create(Role role, Alliance alliance, int rank, boolean online, boolean needAllianceWelcome) {
		AllianceMember allianceMember = newEntityInstance();
		allianceMember.setAllianceId(alliance.getId());
		allianceMember.setPersistKey(role.getId());
		allianceMember.setName(role.getName());
		allianceMember.setHead(role.getHead());
        allianceMember.setHeadFrame(role.getHeadFrame());
		allianceMember.setRank(rank);
		allianceMember.setOnline(online);
		allianceMember.setOfflineTime(role.getLastLogoutTime());
		allianceMember.setJoinTime(TimeUtil.getNow());
		allianceMember.setNeedAllianceWelcome(needAllianceWelcome);
		allianceMember.setMigrateServerId(role.getCurrentServerId());
		return createEntity(alliance, allianceMember);
	}

	@Override
	public void save(AllianceMember entity) {
		if (Application.isBattleServer()) {
			return;
		}
		// alliancemember是alliance子类别，但又跟玩家相关，
		// 本服的联盟回存
		super.save(entity);
	}

	public void loadAllianceMemberFromOtherDB(int db, Long roleId) {
		AllianceMember allianceMem = findById(roleId);
		if (allianceMem != null) {
			log.info("联盟成员已加载{}", roleId);
			return;
		}
		long start = TimeUtil.getNow();
		// 联盟的db不是全用本服的，GVG战斗服需要拉取别的服的联盟，db设置为原服的，并且不回存，联盟下挂的信息包括member、tech等同样需要处理
		MongoCursor<AllianceMember> mongoCursor = dbFindByPrimaryId(db, roleId);
		Collection<AllianceMember> dbFindAll = transform(mongoCursor);
		for (AllianceMember allianceMember : dbFindAll) {
			allianceMember.setDB(db); // 只是 @JsonIgnore
			allianceMember.setoServerId(db);
			allianceMember.setCurrentServerId(Application.getServerId());
			putMemory(allianceMember, false); // 启动服务器时不抛异常
		}
		log.info("{} 从数据库 {} 共加载活跃的实体 {} 个，消耗 {} 毫秒", this.getClass().getSimpleName(), db, dbFindAll.size(), TimeUtil.getNow() - start);
	}

	public AllianceMember removeMemory(Long entityId) {
		AllianceMember entity = this.entities.remove(entityId);
		if (entity != null) {
			bizKeyMap.remove(entity);
			this.removeMemoryIndexes(entity);
		}
		return entity;
	}

	public void deleteAll() {
		delete(findAll());
	}

}
