package com.lc.billion.icefire.game.biz.service.impl.blizzard;

import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.model.unlock.UnlockCondRelationship;
import com.lc.billion.icefire.game.biz.service.impl.unlock.ConditionTrigger;

import java.util.List;

public class BlizzardCondTrigger extends ConditionTrigger {
	public BlizzardCondTrigger(long roleId, List<int[]> conds, UnlockCondRelationship relationship) {
		super(roleId, conds, relationship);
	}

	@Override
	public void run() {
		BlizzardServiceImpl blizzardService = Application.getBean(BlizzardServiceImpl.class);
		if (blizzardService == null) {
			return;
		}
		blizzardService.onCondTrigger(roleId);
	}
}
