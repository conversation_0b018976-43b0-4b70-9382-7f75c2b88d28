package com.lc.billion.icefire.game.biz.config;

import java.util.List;
import java.util.Map;

import com.lc.billion.icefire.core.config.annotation.Config;
import com.lc.billion.icefire.core.config.annotation.MetaMap;
import com.lc.billion.icefire.core.config.model.AbstractMeta;
import com.lc.billion.icefire.game.biz.config.AlliStarMissionConfig.AlliStarMissionMeta;

/**
 * 
 * <AUTHOR>
 * @sine 2015年8月8日 下午3:22:41
 * 
 */
@Config(name = "AlliStarMission", metaClass = AlliStarMissionMeta.class)
public class AlliStarMissionConfig {
	@MetaMap
	private Map<String, AlliStarMissionMeta> metaMap;

	public void init(List<AlliStarMissionMeta> list) {
	}

	public Map<String, AlliStarMissionMeta> getMetaMap() {
		return metaMap;
	}

	public void setMetaMap(Map<String, AlliStarMissionMeta> metaMap) {
		this.metaMap = metaMap;
	}

	public static class AlliStarMissionMeta extends AbstractMeta {

		private String id;
		private String name;
		private long value;

		public String getId() {
			return id;
		}

		public void setId(String id) {
			this.id = id;
		}

		public String getName() {
			return name;
		}

		public void setName(String name) {
			this.name = name;
		}

		public long getValue() {
			return value;
		}

		public void setValue(long value) {
			this.value = value;
		}

	}

}
