package com.lc.billion.icefire.game.biz.async.allianceBattle;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.async.AsyncOperation;
import com.lc.billion.icefire.game.biz.config.AllianceBattleSettingConfig;
import com.lc.billion.icefire.game.biz.dao.impl.RankDaoImpl;
import com.lc.billion.icefire.game.biz.dao.mongo.root.AllianceBattleInfoDao;
import com.lc.billion.icefire.game.biz.dao.mongo.root.AllianceDao;
import com.lc.billion.icefire.game.biz.model.alliance.Alliance;
import com.lc.billion.icefire.game.biz.service.impl.ServiceDependency;
import com.lc.billion.icefire.game.biz.service.impl.allianceBattle.MatchType;
import com.lc.billion.icefire.game.biz.service.impl.rank.RankServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.rank.RankType;
import com.longtech.ls.config.ServerType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.resps.Tuple;

import java.util.*;
import java.util.stream.Collectors;

public class AllianceBattleMatchOperation implements AsyncOperation {
    private final Logger logger = LoggerFactory.getLogger(AllianceBattleMatchOperation.class);

    private final RankDaoImpl rankDao;

    private final ServiceDependency srvDep;

    private final AllianceBattleInfoDao infoDao;

    public AllianceBattleMatchOperation(RankDaoImpl rankDao, ServiceDependency srvDep, AllianceBattleInfoDao infoDao) {
        this.rankDao = rankDao;
        this.srvDep = srvDep;
        this.infoDao = infoDao;
    }

    @Override
    public boolean run() {
        // 匹配的数量
        int matchNum = AllianceBattleSettingConfig.getInstance().getMatchAllianceNum();
        int serverNum = 1;
        // 跨服匹配和非跨服匹配
        if (Application.getServerType() == ServerType.KVK_SEASON) {
            // 跨服时 每个服各占一半
            serverNum = Application.getKVKGroupServerIds().size();
        }

        return serverAllianceMatch(matchNum / serverNum, serverNum);
    }

    /**
     * 跨服匹配
     *
     * @return
     */
    private boolean serverAllianceMatch(int matchNum, int serverNum) {
        // 获取所有的联盟数据
        AllianceDao allianceDao = Application.getBean(AllianceDao.class);
        Map<Integer, Set<Long>> serverAllianceMap = Maps.newHashMapWithExpectedSize(serverNum);
        // 获取所有联盟的势力排名
        var allAllianceRank = rankDao.getRank(
                RankServiceImpl.getKey(RankType.ALLIANCE_FIGHT),
                0,
                -1,
                RankType.ALLIANCE_FIGHT.getRedisType());

        if (allAllianceRank == null || allAllianceRank.isEmpty()) {
            return false;
        }
        Set<Integer> serverIds = Sets.newHashSetWithExpectedSize(serverNum);
        // 遍历排行榜获取不同区服的数量
        for (Tuple rank : allAllianceRank) {
            Long allianceId = Long.valueOf(rank.getElement());
            Alliance alliance = allianceDao.findById(allianceId);
            if (alliance == null) {
                continue;
            }
            int serverId = alliance.getoServerId();
            Set<Long> allianceSet = serverAllianceMap.computeIfAbsent(serverId, k -> new HashSet<>());
            if (allianceSet.size() >= matchNum) {
                serverIds.add(serverId);
                // 多个区服都满足后退出
                if (serverIds.size() == serverNum) {
                    break;
                }
                continue;
            }
            allianceSet.add(allianceId);
        }
        // 所有处理过的联盟
        Set<Long> allCreateIds = Sets.newHashSetWithExpectedSize(matchNum * serverNum);
        // 上面以获取所有的区服需要进行匹配的联盟，计算分支
        List<Long> allianceSortIds = calcMatchScore(serverAllianceMap, matchNum);
        // 匹配
        Long allianceId1 = null;
        Long allianceId2 = null;
        for (Long allianceId : allianceSortIds) {
            if (allianceId1 == null) {
                allianceId1 = allianceId;
            } else {
                allianceId2 = allianceId;
            }
            if (allianceId1 != null && allianceId2 != null) {
                infoDao.create(allianceId1, allianceId2);
                allCreateIds.add(allianceId1);
                allCreateIds.add(allianceId2);
                allianceId1 = null;
                allianceId2 = null;
            }
        }
        if (allianceId1 != null) {
            infoDao.create(allianceId1, 0);
            allCreateIds.add(allianceId1);
        }
        // 对剩余进行匹配
        Collection<Alliance> alliances = allianceDao.findAll();
        for (Alliance alliance : alliances) {
            if (allCreateIds.contains(alliance.getId())) {
                continue;
            }
            infoDao.create(alliance.getId(), 0);
        }

        return false;
    }

    /**
     * 计算匹配积分
     *
     * @param serverAllianceIdMap
     * @return
     */
    private List<Long> calcMatchScore(Map<Integer, Set<Long>> serverAllianceIdMap, int matchNum) {
        Map<Integer, Map<Long, Integer>> serverAllianceScoreMap = Maps.newHashMapWithExpectedSize(serverAllianceIdMap.size());
        // 每个区服单独计算
        Map<Long, Integer> scoreMap = new HashMap<>();
        for (Map.Entry<Integer, Set<Long>> entry : serverAllianceIdMap.entrySet()) {
            Integer serverId = entry.getKey();
            Set<Long> allianceIds = entry.getValue();
            Map<Long, Integer> serverAllianceScore = serverAllianceScoreMap.computeIfAbsent(serverId, k -> Maps.newHashMapWithExpectedSize(allianceIds.size()));
            for (Long allianceId : allianceIds) {
                int score = 0;
                for (MatchType type : MatchType.values()) {
                    score += srvDep.getAllianceBattleService().calAllianceMatchScoreByType(allianceId, type);
                }
                serverAllianceScore.put(allianceId, score);
                scoreMap.put(allianceId, score);
            }
        }
        // 根据积分进行排序
        Map<Integer, List<Long>> sortMap = Maps.newHashMapWithExpectedSize(serverAllianceScoreMap.size());
        serverAllianceScoreMap.forEach((k, v) -> {
            List<Long> list =
                    v.entrySet().stream()
                            .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder()))
                            .map(Map.Entry::getKey)
                            .collect(Collectors.toList());
            sortMap.put(k, list);
        });

        List<Long> result = new ArrayList<>();
        for (int i = 0; i < matchNum; i++) {
            List<Long> tmpList = new ArrayList<>();
            for (List<Long> list : sortMap.values()) {
                if (list.size() <= i) {
                    continue;
                }
                tmpList.add(list.get(i));
            }
            if (!tmpList.isEmpty()) {
                List<Long> sortedList = tmpList.stream()
                        .sorted(Comparator.comparingInt(scoreMap::get).reversed())
                        .collect(Collectors.toList());
                result.addAll(sortedList);
            }
        }

        // 再按照上次同盟总积分排序进行匹配
        // 获取所有联盟的势力排名
        String allianceKey = RankType.ALLIANCE_BATTLE_ALLIANCE_TOTAL.getHandler().getKey(RankType.ALLIANCE_BATTLE_ALLIANCE_TOTAL);
        var allianceBattleRank = rankDao.getRank(
                allianceKey,
                0,
                -1,
                RankType.ALLIANCE_BATTLE_ALLIANCE_TOTAL.getRedisType());
        // 匹配后清空排行榜
        rankDao.clearRank(allianceKey, RankType.ALLIANCE_BATTLE_ALLIANCE_TOTAL.getRedisType());

        if (allianceBattleRank == null || allianceBattleRank.isEmpty()) {
            logger.info("calcMatchScore allianceBattleRank is null. key:{}", allianceKey);
            return result;
        }
        Set<Integer> serverIds = Sets.newHashSetWithExpectedSize(matchNum);
        // 遍历排行榜
        List<Long> newResult = new ArrayList<>();
        Map<Long, Double> newScoreMap = new HashMap<>();
        for (Tuple rank : allianceBattleRank) {
            Long allianceId = Long.valueOf(rank.getElement());
            var score = rank.getScore();
            newScoreMap.put(allianceId, score);
        }
        List<Long> tmpList = new ArrayList<>(result);
        for (var id : tmpList) {
            newScoreMap.putIfAbsent(id, 0D);
        }
        List<Long> sortedList = tmpList.stream()
                .sorted(Comparator.comparingDouble(newScoreMap::get).reversed())
                .collect(Collectors.toList());
        newResult.addAll(sortedList);
        logger.info("calcMatchScore oldScoreMap:{} newScoreMap:{}", scoreMap, newScoreMap);
        logger.info("calcMatchScore oldResult:{} newResult:{}", result, newResult);

        return newResult;
    }

//    /**
//     * 单服匹配
//     *
//     * @return
//     */
//    private boolean singleServerMatch(int matchNum) {
//        Map<Long, Integer> allianceScoreMap = new HashMap<>();
//        //获取需要匹配的联盟
//        AllianceDao allianceDao = Application.getBean(AllianceDao.class);
//        Collection<Long> allAllianceIds = allianceDao.findAllKeys();
//        String rankKey = RankServiceImpl.getKey(RankType.ALLIANCE_FIGHT);
//        var data = rankDao.getRank(rankKey, 0, matchNum - 1, RankType.ALLIANCE_FIGHT.getRedisType());
//        if (data != null) {
//            for (Tuple t : data) {
//                Long allianceId = Long.valueOf(t.getElement());
//                allAllianceIds.remove(allianceId);
//                int allianceScore = 0;
//                for (MatchType type : MatchType.values()) {
//                    allianceScore += srvDep.getAllianceBattleService().calAllianceMatchScoreByType(allianceId, type);
//                }
//                allianceScoreMap.putIfAbsent(allianceId, allianceScore);
//                logger.info("alliance:{}, totalMatchScore:{}", allianceId, allianceScore);
//            }
//
//            //完成积分计算后，按照积分匹配
//            Map<Long, Integer> sortedByValue = allianceScoreMap.entrySet().stream()
//                    .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder()))
//                    .collect(Collectors.toMap(
//                            Map.Entry::getKey,
//                            Map.Entry::getValue,
//                            (oldValue, newValue) -> oldValue,
//                            LinkedHashMap::new
//                    ));
//            Iterator<Map.Entry<Long, Integer>> iterator = sortedByValue.entrySet().iterator();
//            //两两匹配。如果是单数，最后剩下的那个联盟不进入匹配
//            while (iterator.hasNext()) {
//                Map.Entry<Long, Integer> entry1 = iterator.next();
//                if (iterator.hasNext()) {
//                    Map.Entry<Long, Integer> entry2 = iterator.next();
//                    infoDao.create(entry1.getKey(), entry2.getKey());
//                } else {
//                    //单数的话，这个联盟自己玩
//                    infoDao.create(entry1.getKey(), 0);
//                }
//            }
//            //存在超过匹配数量个联盟的话，超过的联盟也自己玩
//            if (!allAllianceIds.isEmpty()) {
//                for (Long unmatchAlliance : allAllianceIds) {
//                    infoDao.create(unmatchAlliance, 0);
//                }
//            }
//        }
//        return true;
//    }

}
