package com.lc.billion.icefire.game.msg.handler.impl.item;

import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.item.ItemServiceImpl;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgItemBathBuyAndUse;
import com.lc.billion.icefire.protocol.structure.PsSimpleItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName CgItemBathBuyAndUseHandler
 * @Description
 * <AUTHOR>
 * @Date 2023/11/13 16:31
 * @Version 1.0
 */
@Controller
public class CgItemBathBuyAndUseHandler extends CgAbstractMessageHandler<CgItemBathBuyAndUse> {

    @Autowired
    public ItemServiceImpl itemSrv;

    @Override
    protected void handle(Role role, CgItemBathBuyAndUse message) {

        List<SimpleItem> useItems = new ArrayList<>();
        for (PsSimpleItem pItem : message.getUseItems()) {
            SimpleItem item = new SimpleItem();
            item.setCount(pItem.getCount());
            item.setMetaId(pItem.getMetaId());
            item.setParam1(pItem.getParam1());
            useItems.add(item);
        }

        List<SimpleItem> buyItems = new ArrayList<>();
        for (PsSimpleItem pItem : message.getBuyItems()) {
            SimpleItem item = new SimpleItem();
            item.setCount(pItem.getCount());
            item.setMetaId(pItem.getMetaId());
            item.setParam1(pItem.getParam1());
            buyItems.add(item);
        }

        itemSrv.bathBuyAndUseItem(role, useItems, buyItems, message.getParam());
    }
}
