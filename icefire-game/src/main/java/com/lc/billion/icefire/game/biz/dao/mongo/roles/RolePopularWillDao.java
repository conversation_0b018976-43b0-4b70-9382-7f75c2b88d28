package com.lc.billion.icefire.game.biz.dao.mongo.roles;

import com.lc.billion.icefire.game.biz.dao.RolesSingleEntityDao;
import com.lc.billion.icefire.game.biz.model.popularwill.RolePopularWill;
import com.lc.billion.icefire.game.biz.model.role.Role;
import org.springframework.stereotype.Repository;

/**
 * @ClassName RolePopularWillDao
 * @Description
 * <AUTHOR>
 * @Date 2024/4/25 16:38
 * @Version 1.0
 */
@Repository
public class RolePopularWillDao extends RolesSingleEntityDao<RolePopularWill> {
    public RolePopularWillDao() {
        super(RolePopularWill.class);
    }

    @Override
    public boolean copy(Role role, Long sourceRoleId, Long targetRoleId) {
        doDelete(targetRoleId);
        RolePopularWill rolePopularWill = create(role);
        RolePopularWill fromRolePopularWill = findById(sourceRoleId);

        rolePopularWill.setMaxValue(fromRolePopularWill.getMaxValue());
        rolePopularWill.setNextReportTime(fromRolePopularWill.getNextReportTime());
        rolePopularWill.setTargetValue(fromRolePopularWill.getTargetValue());
        rolePopularWill.setCurrentValue(fromRolePopularWill.getCurrentValue());
        rolePopularWill.setLastUpdateTime(fromRolePopularWill.getLastUpdateTime());
        rolePopularWill.setPeopleCount(fromRolePopularWill.getPeopleCount());
        rolePopularWill.setWeather(fromRolePopularWill.getWeather());
        rolePopularWill.setStateMaxVal(fromRolePopularWill.getStateMaxVal());
        rolePopularWill.setFatigue(fromRolePopularWill.getFatigue());
        rolePopularWill.setComplaintGuildEntTime(fromRolePopularWill.getComplaintGuildEntTime());
        rolePopularWill.setProtestEndTime(fromRolePopularWill.getProtestEndTime());

        save(rolePopularWill);
        return true;
    }
}
