package com.lc.billion.icefire.game.msg.handler.impl.recharge;

import com.alibaba.fastjson.JSONObject;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.Application;
import com.lc.billion.icefire.game.biz.config.LiBaoConfig.LibaoMeta;
import com.lc.billion.icefire.game.biz.config.LibaoPriceConfig;
import com.lc.billion.icefire.game.biz.config.LibaoPriceConfig.LibaoPriceMeta;
import com.lc.billion.icefire.game.biz.manager.LiBaoGameConfigManager;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.recharge.RechargeServiceImpl;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.game.support.LogReasons.ChargeLogReason;
import com.lc.billion.icefire.protocol.CgPlayerRechargeTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.net.InetSocketAddress;

/**
 * 充值测试 必须在内网环境时，这个接口才能访问成功! 必须在内网环境时，这个接口才能访问成功! 必须在内网环境时，这个接口才能访问成功!
 * 
 * <AUTHOR>
 *
 */
@Controller
public class CgPlayerRechargeTestHandler extends CgAbstractMessageHandler<CgPlayerRechargeTest> {

	// private static final Logger logger =
	// LoggerFactory.getLogger(CgPlayerRechargeTestHandler.class);

	@Autowired
	private RechargeServiceImpl rsImpl;

	@Autowired
	private ConfigServiceImpl configService;

	@Autowired
	private LiBaoGameConfigManager liBaoConfigManager;

	// @Autowired
	// private MonthCardServiceImpl monthCardService;
	// @Autowired
	// private WorldServiceImpl worldService;

	public void handle(Role role, CgPlayerRechargeTest message) {
		// Player player = role.getPlayer();
		InetSocketAddress remoteAddress = role.getPlayer().getNetSession().getRemoteAddress();
		if (remoteAddress.getHostString().startsWith("10.1") || remoteAddress.getHostString().equals("127.0.0.1")) {
			test(message, role);
		}
	}

	private void test(CgPlayerRechargeTest message, Role role) {
		String orderId = message.getOrderId();
		int platform = message.getPlatform();
		String metaId = message.getMetaId();
		String extData = message.getExtData();

//		LibaoMeta liBaoMeta = configService.getConfig(LiBaoConfig.class).getById(metaId);
		LibaoMeta liBaoMeta = liBaoConfigManager.getById(metaId);
		if (liBaoMeta == null) {
			return;
		}

		LibaoPriceConfig libaoPriceConfig = configService.getConfig(LibaoPriceConfig.class);
		LibaoPriceMeta priceMeta = libaoPriceConfig.getById(liBaoMeta.getPriceId());
		if (priceMeta == null) {
			return;
		}

		boolean isDiscount = false;


        LibaoPriceMeta discountPriceMeta = configService.getConfig(LibaoPriceConfig.class).getById(liBaoMeta.getPercentLiBaoPrice());
        if (discountPriceMeta == null) {
            return;
        }
        priceMeta = discountPriceMeta;

        JSONObject result = new JSONObject();

		rsImpl.recharge(role, orderId, metaId, (float) priceMeta.getDollar(), platform, ChargeLogReason.CHARGE_PAYMENT_GIFT, isDiscount, "",
				"lbogboiiinneehgbnpnoangj", "com.lc.cod.gp", 0, "", "",extData, Application.getSeason(), result);
	}

}
