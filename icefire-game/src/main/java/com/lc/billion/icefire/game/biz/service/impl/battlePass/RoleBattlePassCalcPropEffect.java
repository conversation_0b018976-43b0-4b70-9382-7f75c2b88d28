package com.lc.billion.icefire.game.biz.service.impl.battlePass;

import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleBattlePassDao;
import com.lc.billion.icefire.game.biz.model.prop.CalcPropContainer;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.prop.AbstractRoleCalcPropEffect;
import com.lc.billion.icefire.game.biz.service.impl.prop.extention.PropSource;
import com.simfun.sgf.utils.JavaUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class RoleBattlePassCalcPropEffect extends AbstractRoleCalcPropEffect {
    @Autowired
    private RoleBattlePassDao battlePassDao;

    @Override
    public Type getType() {
        return RoleType.BATTLE_PASS;
    }

    @Override
    public PropSource getSource() {
        return PropSource.BATTLE_PASS;
    }

    @Override
    public void calcProps(Role role, CalcPropContainer<?> propCtn, Object... extParam) {
        var bps = battlePassDao.findByRoleId(role.getId());
        if (!JavaUtils.bool(bps)){
            return;
        }

//        BattlePassGroupConfig bpgConfig = configService.getConfig(BattlePassGroupConfig.class);
//        long now = TimeUtil.getNow();
//        for (var bp : bps) {
//            if (bp.isValid(now)) {
//                continue;
//            }
//
//            BattlePassBuyMeta battlePassBuyMeta = bpgConfig.getByGroup(Integer.parseInt(activityListMeta.getGroup()));
//            if(battlePassBuyMeta != null && battlePassBuyMeta.getPayProperty() != null){
//                PropUtils.calcRoleProps(role, propCtn, battlePassBuyMeta.getPayProperty());
//            }
//        }
    }
}
