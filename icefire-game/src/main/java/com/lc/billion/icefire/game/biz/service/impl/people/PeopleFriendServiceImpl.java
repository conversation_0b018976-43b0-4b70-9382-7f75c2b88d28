package com.lc.billion.icefire.game.biz.service.impl.people;

import com.lc.billion.icefire.game.biz.async.people.PeopleFriendNameCheckWordOperation;
import com.lc.billion.icefire.game.biz.async.people.PeopleWordOperation;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RolePeopleDao;
import com.lc.billion.icefire.game.biz.manager.RoleManager;
import com.lc.billion.icefire.game.biz.model.people.People;
import com.lc.billion.icefire.game.biz.model.people.PeopleBornConditionType;
import com.lc.billion.icefire.game.biz.model.people.PeopleFriend;
import com.lc.billion.icefire.game.biz.model.people.RolePeople;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.AsyncOperationServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName PeopleFriendServiceImpl
 * @Description
 * <AUTHOR>
 * @Date 2024/8/24 15:47
 * @Version 1.0
 */
@Service
public class PeopleFriendServiceImpl {

    private final static Logger logger = LoggerFactory.getLogger(PeopleFriendServiceImpl.class);

    @Autowired
    private RolePeopleDao rolePeopleDao;
    @Autowired
    private RoleManager roleManager;
    @Autowired
    private PeopleServiceImpl peopleService;
    @Autowired
    private AsyncOperationServiceImpl asyncOpServiceImpl;

    private RolePeople getRolePeople(Role role) {
        return rolePeopleDao.findById(role.getRoleId());
    }

    public boolean callbackDismissalFriend(Role role, long friendAccId, long friendRoleId) {
        RolePeople rolePeople = getRolePeople(role);
        if (rolePeople == null) {
            return false;
        }
        if (!rolePeople.isSharedToFriend(friendRoleId, friendAccId)) {
            return false;
        }
        boolean result = rolePeople.getSharedFriends().removeIf(friend -> friend.getRoleId() == friendRoleId);
//        if (result) {
//            // 删除好友绑定关系
//            rolePeople.getSharedFriends().removeIf(friend -> friend.getRoleId() == friendRoleId && friend.getAccountId() == friendAccId);
//        }
        return result;
    }

    public void onRpcCallbackDismissalFriend(long roleId, long friendAccId, long friendRoleId) {
        logger.info("onRpcCallbackDismissalFriend roleId:{}, friendAccId:{}, friendRoleId:{}", roleId, friendAccId, friendRoleId);
        Role role = roleManager.getRole(roleId);
        if (role == null) {
            return;
        }
        callbackDismissalFriend(role, friendAccId, friendRoleId);
    }


    public void onRpcCallbackAddFriendPeople(long roleId, long friendAccId, long friendRoleId) {
        logger.info("onRpcCallbackAddFriendPeople roleId:{}, friendAccId:{}, friendRoleId:{}", roleId, friendAccId, friendRoleId);
        Role role = roleManager.getRole(roleId);
        if (role == null) {
            return;
        }
        onCallbackAddFriendPeople(role, friendAccId, friendRoleId);
    }


    /**
     * 给好友加好朋友小人的回调，更新自己的朋友依赖关系
     *
     * @param role
     * @param friendAccId
     * @param friendId
     * @return
     */
    public boolean onCallbackAddFriendPeople(Role role, long friendAccId, long friendId) {
        RolePeople rolePeople = getRolePeople(role);
        if (rolePeople == null) {
            return false;
        }
        if (rolePeople.isSharedToFriend(friendId, friendAccId)) {
            return true;
        }
        rolePeople.getSharedFriends().add(new PeopleFriend(friendId, friendAccId));
        rolePeopleDao.save(rolePeople);
        return true;
    }

    /**
     * 收到好友的点击链接 增加朋友小人
     *
     * @param roleId
     * @param typeId
     * @param params
     */
    public void onRpcFriendGivePerson(Long roleId, int typeId, Object... params) {
        logger.info("onRpcFriendGivePerson roleId:{}, typeId:{}", roleId, typeId);
        Role role = roleManager.getRole(roleId);
        if (role == null) {
            return;
        }
        PeopleBornConditionType type = PeopleBornConditionType.findById(typeId);
        if (type == null) {
            return;
        }
        peopleService.friendGivePerson(role, type, params);
    }

    /**
     * 好友修改名字或者头像
     *
     * @param roleId
     * @param friendId
     * @param friendName
     * @param friendHead
     */
    public void onRpcFriendInfoChange(long roleId, long friendId, String friendName, String friendHead, int friendSex) {
        logger.info("onRpcFriendInfoChange roleId:{}, friendId:{}, friendName:{}, friendHead:{}", roleId, friendId, friendName, friendHead);
        Role role = roleManager.getRole(roleId);
        if (role == null) {
            return;
        }
        onFriendInfoChange(role, friendId, friendName, friendHead, friendSex);
    }

    public void onFriendInfoChange(Role role, long friendId, String friendName, String friendHead, int friendSex) {
        logger.info("onFriendInfoChange roleId:{}, friendId:{}, friendName:{}, friendHead:{}", role.getRoleId(), friendId, friendName, friendHead);
        RolePeople rolePeople = getRolePeople(role);
        if (rolePeople == null) {
            return;
        }
        List<People> changePeoples = new ArrayList<>();
        rolePeople.getPeopleMap().values().forEach(people -> {
            if (people.getFriendId() == friendId) {
                people.setName(friendName);
                people.setHead(friendHead);
                if (people.getSex() != friendSex) {
                    asyncOpServiceImpl.execute(new PeopleWordOperation(role, people, friendSex,true, 0));
                    // rolePeople.changePropertyBySex(people, friendSex); // 性别影响词条，不再同步计算
                }
                changePeoples.add(people);
            }
        });
        if (!changePeoples.isEmpty()) { // 其他信息变更该立即下发的还是立即下发
            role.send(PeopleOutPut.wrapperGcPeopleChangeNotify(changePeoples));
        }
    }

    public void gmChangeSex(Role role, int peopleId, int newSex) {
        RolePeople rolePeople = getRolePeople(role);
        if (rolePeople == null) {
            return;
        }

        People people = rolePeople.findPeople(peopleId);
        if (people == null || people.getSex() == newSex) {
            return;
        }

        asyncOpServiceImpl.execute(new PeopleWordOperation(role, people, newSex, true, 0));
    }
}
