package com.lc.billion.icefire.game.biz.service.impl.libao.action;

import com.lc.billion.icefire.game.biz.config.LiBaoConfig;
import com.lc.billion.icefire.game.biz.manager.LiBaoGameConfigManager;
import com.lc.billion.icefire.game.biz.manager.RoleLibaoManager;
import com.lc.billion.icefire.game.biz.model.libao.LiBaoActionParam;
import com.lc.billion.icefire.game.biz.model.libao.LiBaoActionType;
import com.lc.billion.icefire.game.biz.model.libao.RoleLibaoRecord;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.libao.LiBaoContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class LiBaoActionDisableGroupOther extends LiBaoAction {
    private static final Logger logger = LoggerFactory.getLogger(LiBaoActionDisableGroupOther.class);

    @Autowired
    private LiBaoGameConfigManager liBaoConfigManager;
    @Autowired
    private RoleLibaoManager roleLiBaoManager;

    public LiBaoActionType getType() {
        return LiBaoActionType.DISABLE_GROUP_OTHER;
    }

    /**
     * 处理 action
     *
     * @param role
     * @param action
     * @return
     */
    public void doAction(Role role, LiBaoActionParam action, LiBaoContext libaoContext, RoleLibaoRecord record) {
        String metaId = record.getMetaId();
        List<LiBaoConfig.LibaoMeta> otherMeta = liBaoConfigManager.getGroupOther(metaId);
        if (otherMeta == null) {
//            logger.error("libao meta not found {}", metaId);
            return;
        }

        for (LiBaoConfig.LibaoMeta meta: otherMeta) {
            RoleLibaoRecord otherRecord = roleLiBaoManager.getRoleLibaoRecord(role.getRoleId(), meta.getId());
            otherRecord.setEnable(false);
            libaoContext.addUpdateLiBao(otherRecord);
        }
    }
}
