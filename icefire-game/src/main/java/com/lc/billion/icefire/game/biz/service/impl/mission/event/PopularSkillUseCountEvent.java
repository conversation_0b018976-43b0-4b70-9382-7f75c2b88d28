package com.lc.billion.icefire.game.biz.service.impl.mission.event;

import com.lc.billion.icefire.game.biz.config.MissionConfig;
import com.lc.billion.icefire.game.biz.model.mission.IMissionItem;
import com.lc.billion.icefire.game.biz.model.mission.AbstractBaseMissionItem;
import com.lc.billion.icefire.game.biz.model.mission.MissionType;

/**
 * @ClassName PopularSkillUseCountEvent
 * @Description
 * <AUTHOR>
 * @Date 2024/7/1 15:51
 * @Version 1.0
 */
public class PopularSkillUseCountEvent extends AbstractMissionEvent {
    @Override
    public MissionType getType() {
        return MissionType.POPULAR_SKILL_USE_COUNT;
    }


    @Override
    public boolean effect(IMissionItem item, MissionConfig.MissionMeta meta, Object... params) {
        int idNum = (int) params[0];
        if (idNum <= 0) {
            return false;
        }
        long amount = item.getProgress() + idNum;
        setAmount(item, meta, amount, false);
        return true;
    }
}
