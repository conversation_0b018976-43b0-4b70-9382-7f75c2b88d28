package com.lc.billion.icefire.game.biz.service.impl.activity.handler;

import com.lc.billion.icefire.game.biz.manager.activity.ActivityCommonManager;
import com.lc.billion.icefire.game.biz.manager.activity.SnowActivityManager;
import com.lc.billion.icefire.game.biz.model.activity.ActivityType;
import com.lc.billion.icefire.game.biz.service.impl.activity.handler.common.ActivityCommonHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class snowEventHandler extends ActivityCommonHandler {
    @Autowired
    private SnowActivityManager snowActivityManager;
    @Override
    public ActivityCommonManager getManager() {
        return snowActivityManager;
    }

    @Override
    public ActivityType getType() {
        return ActivityType.SNOW_EVENT;
    }
}
