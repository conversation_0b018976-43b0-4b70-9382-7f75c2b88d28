package com.lc.billion.icefire.game.biz.model.expedition;

import com.lc.billion.icefire.protocol.structure.PsMBDLargSkillUnit;

/**
 * @ClassName ExpeditionLargeSkillUnit
 * @Description
 * <AUTHOR>
 * @Date 2023/12/11 21:41
 * @Version 1.0
 */
public class ExpeditionLargeSkillUnit {
    private int frameNum;
    private int posIndex;

    public ExpeditionLargeSkillUnit() {
            
    }

    public ExpeditionLargeSkillUnit(PsMBDLargSkillUnit data) {
        frameNum = data.getFrameNum();
        posIndex = data.getPosIndex();
    }

    public int getFrameNum() {
        return frameNum;
    }

    public void setFrameNum(int frameNum) {
        this.frameNum = frameNum;
    }

    public int getPosIndex() {
        return posIndex;
    }

    public void setPosIndex(int posIndex) {
        this.posIndex = posIndex;
    }
}
