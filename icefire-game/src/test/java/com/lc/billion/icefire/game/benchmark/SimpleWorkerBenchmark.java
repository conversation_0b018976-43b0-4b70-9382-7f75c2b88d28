package com.lc.billion.icefire.game.benchmark;

import com.lmax.disruptor.YieldingWaitStrategy;
import com.simfun.sgf.thread.SingleThreadTaskWorker;
import com.simfun.sgf.thread2.SingleThreadDisruptorTaskWorker;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.infra.Blackhole;
import org.openjdk.jmh.runner.Runner;
import org.openjdk.jmh.runner.RunnerException;
import org.openjdk.jmh.runner.options.Options;
import org.openjdk.jmh.runner.options.OptionsBuilder;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 简化的JMH基准测试 - 快速测试版本
 * 
 * 适合在IDE中快速运行和调试的简化版本
 * 
 */
@BenchmarkMode(Mode.Throughput)
@OutputTimeUnit(TimeUnit.SECONDS)
@State(Scope.Benchmark)
@Warmup(iterations = 2, time = 1, timeUnit = TimeUnit.SECONDS)  // 简化预热
@Measurement(iterations = 3, time = 2, timeUnit = TimeUnit.SECONDS)  // 简化测量
@Fork(1)
@Threads(1)
public class SimpleWorkerBenchmark {

    private static final int MESSAGE_COUNT = 1000;  // 固定消息数量，便于快速测试

    // 测试消息类
    static class SimpleMessage {
        private final int id;
        private final long timestamp;
        
        public SimpleMessage(int id) {
            this.id = id;
            this.timestamp = System.nanoTime();
        }
        
        public int getId() {
            return id;
        }
        
        public long getTimestamp() {
            return timestamp;
        }
    }

    // SingleThreadTaskWorker 简化实现
    static class SimpleSingleThreadTaskWorker extends SingleThreadTaskWorker<SimpleMessage> {
        private final AtomicInteger processedCount = new AtomicInteger();
        private volatile boolean running = true;
        
        public SimpleSingleThreadTaskWorker() {
            super("SimpleSingleThreadTaskWorker");
        }
        
        @Override
        protected void execute(SimpleMessage task) {
            // 最小化工作量
            Blackhole.consumeCPU(1);
            processedCount.incrementAndGet();
        }
        
        public void submitTask(SimpleMessage message) {
            addTask(message);
        }
        
        public int getProcessedCount() {
            return processedCount.get();
        }
        
        public void reset() {
            processedCount.set(0);
            running = true;
        }
        
        public void shutdown() {
            running = false;
            super.stop();
        }
    }

    // SingleThreadDisruptorTaskWorker 简化实现
    static class SimpleDisruptorTaskWorker extends SingleThreadDisruptorTaskWorker<SimpleMessage> {
        private final AtomicInteger processedCount = new AtomicInteger();
        
        // 无参构造函数 - Builder反射调用需要
        public SimpleDisruptorTaskWorker() {
        }
        
        @Override
        protected void execute(SimpleMessage task, Object... attach) {
            // 最小化工作量
            Blackhole.consumeCPU(1);
            processedCount.incrementAndGet();
        }
        
        public int getProcessedCount() {
            return processedCount.get();
        }
        
        public void reset() {
            processedCount.set(0);
        }
        
        public void submitTask(SimpleMessage message) {
            addTask(message);
        }
    }

    // 测试状态
    private SimpleSingleThreadTaskWorker singleThreadWorker;
    private SingleThreadDisruptorTaskWorker<SimpleMessage> disruptorWorker;
    private SimpleMessage[] messages;

    @Setup(Level.Trial)
    public void setupTrial() {
        // 预创建消息
        messages = new SimpleMessage[MESSAGE_COUNT];
        for (int i = 0; i < MESSAGE_COUNT; i++) {
            messages[i] = new SimpleMessage(i);
        }
    }

    @Setup(Level.Iteration)
    public void setupIteration() {
        // 初始化SingleThreadTaskWorker
        singleThreadWorker = new SimpleSingleThreadTaskWorker();
        singleThreadWorker.start();
        
        // 初始化DisruptorTaskWorker
        disruptorWorker = SingleThreadDisruptorTaskWorker
                .<SimpleMessage>newBuilder(SimpleDisruptorTaskWorker.class)
                .setName("SimpleDisruptorWorker")
                .setBufferSize(2048)  // 较小的buffer size
                .setWaitStrategy(new YieldingWaitStrategy())
                .build();
        
        if (disruptorWorker != null) {
            disruptorWorker.start();
        }
    }

    @TearDown(Level.Iteration)
    public void tearDownIteration() {
        if (singleThreadWorker != null) {
            singleThreadWorker.shutdown();
        }
        if (disruptorWorker != null) {
            disruptorWorker.stop();
        }
    }

    /**
     * 基准测试：SingleThreadTaskWorker
     */
    @Benchmark
    public long benchmarkSingleThreadTaskWorker(Blackhole blackhole) throws InterruptedException {
        singleThreadWorker.reset();
        
        // 提交所有消息
        for (SimpleMessage message : messages) {
            singleThreadWorker.submitTask(message);
        }
        
        // 等待处理完成
        long startTime = System.nanoTime();
        while (singleThreadWorker.getProcessedCount() < MESSAGE_COUNT) {
            if (System.nanoTime() - startTime > TimeUnit.SECONDS.toNanos(5)) {
                throw new RuntimeException("SingleThreadTaskWorker 处理超时");
            }
            Thread.yield();
        }
        
        int processed = singleThreadWorker.getProcessedCount();
        blackhole.consume(processed);
        return processed;
    }

    /**
     * 基准测试：SingleThreadDisruptorTaskWorker
     */
    @Benchmark
    public long benchmarkDisruptorTaskWorker(Blackhole blackhole) throws InterruptedException {
        if (disruptorWorker == null) {
            return 0;
        }
        
        ((SimpleDisruptorTaskWorker) disruptorWorker).reset();
        
        // 提交所有消息
        for (SimpleMessage message : messages) {
            ((SimpleDisruptorTaskWorker) disruptorWorker).submitTask(message);
        }
        
        // 等待处理完成
        long startTime = System.nanoTime();
        while (((SimpleDisruptorTaskWorker) disruptorWorker).getProcessedCount() < MESSAGE_COUNT) {
            if (System.nanoTime() - startTime > TimeUnit.SECONDS.toNanos(5)) {
                throw new RuntimeException("DisruptorTaskWorker 处理超时");
            }
            Thread.yield();
        }
        
        int processed = ((SimpleDisruptorTaskWorker) disruptorWorker).getProcessedCount();
        blackhole.consume(processed);
        return processed;
    }

    /**
     * 运行简化基准测试的主方法
     */
    public static void main(String[] args) throws RunnerException {
        System.out.println("开始运行简化的Worker基准测试...");
        
        Options opt = new OptionsBuilder()
                .include(SimpleWorkerBenchmark.class.getSimpleName())
                .shouldFailOnError(true)
                .shouldDoGC(true)
                .resultFormat(org.openjdk.jmh.results.format.ResultFormatType.TEXT)  // 文本格式输出
                .build();

        new Runner(opt).run();
        
        System.out.println("基准测试完成！");
    }
} 