package com.lc.billion.icefire.game.benchmark;

import com.lmax.disruptor.YieldingWaitStrategy;
import com.simfun.sgf.thread.SingleThreadTaskWorker;
import com.simfun.sgf.thread2.SingleThreadDisruptorTaskWorker;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * SingleThreadTaskWorker vs SingleThreadDisruptorTaskWorker 性能基准测试
 * 参考 MainWorker 的实现进行性能对比
 */
public class WorkerBenchmarkTest {
    
    private static final Logger logger = LoggerFactory.getLogger(WorkerBenchmarkTest.class);
    
    // 测试消息类
    static class TestMessage {
        private final String name;
        private final long timestamp;
        private final String payload;
        
        public TestMessage(String name, String payload) {
            this.name = name;
            this.timestamp = System.nanoTime();
            this.payload = payload;
        }
        
        public String getName() {
            return name;
        }
        
        public long getTimestamp() {
            return timestamp;
        }
        
        public String getPayload() {
            return payload;
        }
    }
    
    // SingleThreadTaskWorker 实现类 - 使用流式处理模拟任务处理
    static class TestSingleThreadTaskWorker extends SingleThreadTaskWorker<TestMessage> {
        private final AtomicInteger processedCount = new AtomicInteger();
        private final AtomicLong totalLatency = new AtomicLong();
        private final CountDownLatch latch;
        private final int targetCount;
        private volatile boolean shouldStop = false;
        
        public TestSingleThreadTaskWorker(CountDownLatch latch, int targetCount) {
            super("TestSingleThreadTaskWorker");
            this.latch = latch;
            this.targetCount = targetCount;
        }
        
        @Override
        protected void execute(TestMessage task) {
            try {
                // 计算延迟
                long latency = System.nanoTime() - task.getTimestamp();
                totalLatency.addAndGet(latency);
                
                // 模拟业务处理耗时（微秒级）
                Thread.sleep(0, 1000); // 1微秒
                
                int count = processedCount.incrementAndGet();
                if (count >= targetCount) {
                    shouldStop = true;
                    latch.countDown();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        public void submitTask(TestMessage message) {
            addTask(message);
        }
        
        public int getProcessedCount() {
            return processedCount.get();
        }
        
        public double getAverageLatency() {
            int count = processedCount.get();
            return count > 0 ? totalLatency.get() / (double) count : 0;
        }
    }
    
    // SingleThreadDisruptorTaskWorker 实现类
    static class TestDisruptorTaskWorker extends SingleThreadDisruptorTaskWorker<TestMessage> {
        private final AtomicInteger processedCount = new AtomicInteger();
        private final AtomicLong totalLatency = new AtomicLong();
        private CountDownLatch latch;
        private int targetCount;
        
        // 无参构造函数 - Builder反射调用需要
        public TestDisruptorTaskWorker() {
        }
        
        public TestDisruptorTaskWorker(CountDownLatch latch, int targetCount) {
            this.latch = latch;
            this.targetCount = targetCount;
        }
        
        @Override
        protected void execute(TestMessage task, Object... attach) {
            try {
                // 计算延迟
                long latency = System.nanoTime() - task.getTimestamp();
                totalLatency.addAndGet(latency);
                
                // 模拟业务处理耗时（微秒级）
                Thread.sleep(0, 1000); // 1微秒
                
                int count = processedCount.incrementAndGet();
                if (count >= targetCount && latch != null) {
                    latch.countDown();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        public int getProcessedCount() {
            return processedCount.get();
        }
        
        public double getAverageLatency() {
            int count = processedCount.get();
            return count > 0 ? totalLatency.get() / (double) count : 0;
        }
        
        public void submitTask(TestMessage message) {
            addTask(message);
        }
    }
    
    @Test
    public void quickBenchmark() throws InterruptedException {
        logger.info("快速基准测试 - 验证测试是否正常运行");
        
        int messageCount = 1000;
        
        // 测试 SingleThreadTaskWorker
        BenchmarkResult singleThreadResult = benchmarkSingleThreadTaskWorker(messageCount);
        
        // 测试 SingleThreadDisruptorTaskWorker
        BenchmarkResult disruptorResult = benchmarkDisruptorTaskWorker(messageCount);
        
        // 输出对比结果
        printComparisonResults(messageCount, singleThreadResult, disruptorResult);
    }
    
    @Test
    public void benchmarkComparison() throws InterruptedException {
        logger.info("开始 SingleThreadTaskWorker vs SingleThreadDisruptorTaskWorker 性能基准测试");
        
        // 测试参数
        int[] messageCounts = {10000, 50000, 100000, 500000};
        int warmupCount = 1000;
        
        for (int messageCount : messageCounts) {
            logger.info("\n========== 测试消息数量: {} ==========", messageCount);
            
            // 预热
            warmup(warmupCount);
            
            // 测试 SingleThreadTaskWorker
            BenchmarkResult singleThreadResult = benchmarkSingleThreadTaskWorker(messageCount);
            
            // 测试 SingleThreadDisruptorTaskWorker
            BenchmarkResult disruptorResult = benchmarkDisruptorTaskWorker(messageCount);
            
            // 输出对比结果
            printComparisonResults(messageCount, singleThreadResult, disruptorResult);
        }
    }
    
    private void warmup(int count) throws InterruptedException {
        logger.info("预热测试 - 处理 {} 个消息", count);
        
        CountDownLatch warmupLatch = new CountDownLatch(1);
        SingleThreadDisruptorTaskWorker<TestMessage> warmupWorker = SingleThreadDisruptorTaskWorker
                .<TestMessage>newBuilder(TestDisruptorTaskWorker.class)
                .setName("WarmupWorker")
                .setBufferSize(1024)
                .build();
        
        if (warmupWorker != null) {
            ((TestDisruptorTaskWorker) warmupWorker).latch = warmupLatch;
            ((TestDisruptorTaskWorker) warmupWorker).targetCount = count;
            warmupWorker.start();
            
            for (int i = 0; i < count; i++) {
                ((TestDisruptorTaskWorker) warmupWorker).submitTask(new TestMessage("warmup-" + i, "warmup payload"));
            }
            
            warmupLatch.await(5, TimeUnit.SECONDS);
            warmupWorker.stop();
        }
        
        // 等待系统稳定
        Thread.sleep(100);
    }
    
    private BenchmarkResult benchmarkSingleThreadTaskWorker(int messageCount) throws InterruptedException {
        logger.info("测试 SingleThreadTaskWorker - 消息数量: {}", messageCount);
        
        CountDownLatch latch = new CountDownLatch(1);
        TestSingleThreadTaskWorker worker = new TestSingleThreadTaskWorker(latch, messageCount);
        
        // 记录开始时间和内存
        long startTime = System.nanoTime();
        long startMemory = getUsedMemory();
        
        worker.start();
        
        // 提交所有消息
        for (int i = 0; i < messageCount; i++) {
            worker.submitTask(new TestMessage("test-" + i, "test payload " + i));
        }
        
        // 等待处理完成（最多等待30秒）
        boolean completed = latch.await(30, TimeUnit.SECONDS);
        
        long endTime = System.nanoTime();
        long endMemory = getUsedMemory();
        
        worker.stop();
        
        if (!completed) {
            logger.warn("SingleThreadTaskWorker 测试超时！");
            return new BenchmarkResult("SingleThreadTaskWorker", messageCount, 0, 0, 0, 0, false);
        }
        
        long durationNs = endTime - startTime;
        double throughput = messageCount * 1_000_000_000.0 / durationNs; // messages/second
        double avgLatency = worker.getAverageLatency();
        long memoryUsed = endMemory - startMemory;
        
        return new BenchmarkResult("SingleThreadTaskWorker", messageCount, durationNs, throughput, avgLatency, memoryUsed, true);
    }
    
    private BenchmarkResult benchmarkDisruptorTaskWorker(int messageCount) throws InterruptedException {
        logger.info("测试 SingleThreadDisruptorTaskWorker - 消息数量: {}", messageCount);
        
        CountDownLatch latch = new CountDownLatch(1);
        SingleThreadDisruptorTaskWorker<TestMessage> worker = SingleThreadDisruptorTaskWorker
                .<TestMessage>newBuilder(TestDisruptorTaskWorker.class)
                .setName("BenchmarkDisruptorWorker")
                .setBufferSize(Math.max(1024, Integer.highestOneBit(messageCount) * 2))
                .setWaitStrategy(new YieldingWaitStrategy())
                .build();
        
        if (worker == null) {
            logger.error("创建 DisruptorTaskWorker 失败！");
            return new BenchmarkResult("SingleThreadDisruptorTaskWorker", messageCount, 0, 0, 0, 0, false);
        }
        
        ((TestDisruptorTaskWorker) worker).latch = latch;
        ((TestDisruptorTaskWorker) worker).targetCount = messageCount;
        
        // 记录开始时间和内存
        long startTime = System.nanoTime();
        long startMemory = getUsedMemory();
        
        worker.start();
        
        // 提交所有消息
        for (int i = 0; i < messageCount; i++) {
            ((TestDisruptorTaskWorker) worker).submitTask(new TestMessage("test-" + i, "test payload " + i));
        }
        
        // 等待处理完成（最多等待30秒）
        boolean completed = latch.await(30, TimeUnit.SECONDS);
        
        long endTime = System.nanoTime();
        long endMemory = getUsedMemory();
        
        worker.stop();
        
        if (!completed) {
            logger.warn("SingleThreadDisruptorTaskWorker 测试超时！");
            return new BenchmarkResult("SingleThreadDisruptorTaskWorker", messageCount, 0, 0, 0, 0, false);
        }
        
        long durationNs = endTime - startTime;
        double throughput = messageCount * 1_000_000_000.0 / durationNs; // messages/second
        double avgLatency = ((TestDisruptorTaskWorker) worker).getAverageLatency();
        long memoryUsed = endMemory - startMemory;
        
        return new BenchmarkResult("SingleThreadDisruptorTaskWorker", messageCount, durationNs, throughput, avgLatency, memoryUsed, true);
    }
    
    private void printComparisonResults(int messageCount, BenchmarkResult singleThread, BenchmarkResult disruptor) {
        logger.info("\n========== 性能对比结果 (消息数量: {}) ==========", messageCount);
        
        if (!singleThread.completed || !disruptor.completed) {
            logger.warn("测试未完成，无法进行对比");
            return;
        }
        
        logger.info("SingleThreadTaskWorker:");
        logger.info("  处理时间: {:.2f} ms", singleThread.durationNs / 1_000_000.0);
        logger.info("  吞吐量: {:.0f} messages/second", singleThread.throughput);
        logger.info("  平均延迟: {:.2f} ns", singleThread.avgLatency);
        logger.info("  内存使用: {} bytes", singleThread.memoryUsed);
        
        logger.info("SingleThreadDisruptorTaskWorker:");
        logger.info("  处理时间: {:.2f} ms", disruptor.durationNs / 1_000_000.0);
        logger.info("  吞吐量: {:.0f} messages/second", disruptor.throughput);
        logger.info("  平均延迟: {:.2f} ns", disruptor.avgLatency);
        logger.info("  内存使用: {} bytes", disruptor.memoryUsed);
        
        // 性能对比
        double throughputImprovement = (disruptor.throughput - singleThread.throughput) / singleThread.throughput * 100;
        double latencyImprovement = (singleThread.avgLatency - disruptor.avgLatency) / singleThread.avgLatency * 100;
        double memoryDiff = disruptor.memoryUsed - singleThread.memoryUsed;
        
        logger.info("\n性能对比:");
        logger.info("  吞吐量提升: {:.2f}%", throughputImprovement);
        logger.info("  延迟改善: {:.2f}%", latencyImprovement);
        logger.info("  内存差异: {} bytes", memoryDiff);
        
        if (throughputImprovement > 0) {
            logger.info("  结论: DisruptorTaskWorker 吞吐量更高");
        } else {
            logger.info("  结论: SingleThreadTaskWorker 吞吐量更高");
        }
        
        if (latencyImprovement > 0) {
            logger.info("  结论: DisruptorTaskWorker 延迟更低");
        } else {
            logger.info("  结论: SingleThreadTaskWorker 延迟更低");
        }
    }
    
    private long getUsedMemory() {
        Runtime runtime = Runtime.getRuntime();
        return runtime.totalMemory() - runtime.freeMemory();
    }
    
    // 基准测试结果类
    static class BenchmarkResult {
        final String workerType;
        final int messageCount;
        final long durationNs;
        final double throughput;
        final double avgLatency;
        final long memoryUsed;
        final boolean completed;
        
        BenchmarkResult(String workerType, int messageCount, long durationNs, double throughput, 
                       double avgLatency, long memoryUsed, boolean completed) {
            this.workerType = workerType;
            this.messageCount = messageCount;
            this.durationNs = durationNs;
            this.throughput = throughput;
            this.avgLatency = avgLatency;
            this.memoryUsed = memoryUsed;
            this.completed = completed;
        }
    }
    
    @Test
    public void stressTest() throws InterruptedException {
        logger.info("开始压力测试...");
        
        int messageCount = 1_000_000;
        int producerThreads = 4;
        
        // DisruptorTaskWorker 压力测试
        logger.info("DisruptorTaskWorker 压力测试 - {} 个线程，{} 条消息", producerThreads, messageCount);
        
        CountDownLatch latch = new CountDownLatch(1);
        SingleThreadDisruptorTaskWorker<TestMessage> worker = SingleThreadDisruptorTaskWorker
                .<TestMessage>newBuilder(TestDisruptorTaskWorker.class)
                .setName("StressTestWorker")
                .setBufferSize(1024 * 1024) // 1MB buffer
                .setWaitStrategy(new YieldingWaitStrategy())
                .build();
        
        if (worker != null) {
            ((TestDisruptorTaskWorker) worker).latch = latch;
            ((TestDisruptorTaskWorker) worker).targetCount = messageCount;
            
            long startTime = System.nanoTime();
            worker.start();
            
            // 多线程生产消息
            CountDownLatch producerLatch = new CountDownLatch(producerThreads);
            int messagesPerProducer = messageCount / producerThreads;
            
            for (int t = 0; t < producerThreads; t++) {
                final int threadId = t;
                new Thread(() -> {
                    try {
                        for (int i = 0; i < messagesPerProducer; i++) {
                            ((TestDisruptorTaskWorker) worker).submitTask(new TestMessage("stress-" + threadId + "-" + i, "stress payload"));
                        }
                    } finally {
                        producerLatch.countDown();
                    }
                }, "Producer-" + t).start();
            }
            
            // 等待生产完成
            producerLatch.await();
            
            // 等待处理完成
            boolean completed = latch.await(60, TimeUnit.SECONDS);
            long endTime = System.nanoTime();
            
            worker.stop();
            
            if (completed) {
                long durationNs = endTime - startTime;
                double throughput = messageCount * 1_000_000_000.0 / durationNs;
                logger.info("压力测试完成:");
                logger.info("  处理时间: {:.2f} ms", durationNs / 1_000_000.0);
                logger.info("  吞吐量: {:.0f} messages/second", throughput);
                logger.info("  平均延迟: {:.2f} ns", ((TestDisruptorTaskWorker) worker).getAverageLatency());
            } else {
                logger.warn("压力测试超时！");
            }
        }
    }
} 