/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.structure;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 皇权记录
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class PsOfficialHistory implements org.apache.thrift.TBase<PsOfficialHistory, PsOfficialHistory._Fields>, java.io.Serializable, Cloneable, Comparable<PsOfficialHistory> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PsOfficialHistory");

  private static final org.apache.thrift.protocol.TField START_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("startTime", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField END_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("endTime", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField ROLE_FIELD_DESC = new org.apache.thrift.protocol.TField("role", org.apache.thrift.protocol.TType.STRUCT, (short)3);
  private static final org.apache.thrift.protocol.TField ORDER_FIELD_DESC = new org.apache.thrift.protocol.TField("order", org.apache.thrift.protocol.TType.I32, (short)4);
  private static final org.apache.thrift.protocol.TField META_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("metaId", org.apache.thrift.protocol.TType.STRING, (short)5);
  private static final org.apache.thrift.protocol.TField TARGET_ROLE_FIELD_DESC = new org.apache.thrift.protocol.TField("targetRole", org.apache.thrift.protocol.TType.STRUCT, (short)6);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new PsOfficialHistoryStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new PsOfficialHistoryTupleSchemeFactory();

  /**
   * 开始时间戳 *
   */
  public long startTime; // optional
  /**
   * 结束时间戳 *
   */
  public long endTime; // optional
  /**
   * 操作,官职玩家信息 *
   */
  public @org.apache.thrift.annotation.Nullable PsRoleInfo role; // optional
  /**
   * 太子第几届 *
   */
  public int order; // optional
  /**
   * 嘉奖,技能配置id *
   */
  public @org.apache.thrift.annotation.Nullable java.lang.String metaId; // optional
  /**
   * 嘉奖,技能的使用对象 *
   */
  public @org.apache.thrift.annotation.Nullable PsRoleInfo targetRole; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 开始时间戳 *
     */
    START_TIME((short)1, "startTime"),
    /**
     * 结束时间戳 *
     */
    END_TIME((short)2, "endTime"),
    /**
     * 操作,官职玩家信息 *
     */
    ROLE((short)3, "role"),
    /**
     * 太子第几届 *
     */
    ORDER((short)4, "order"),
    /**
     * 嘉奖,技能配置id *
     */
    META_ID((short)5, "metaId"),
    /**
     * 嘉奖,技能的使用对象 *
     */
    TARGET_ROLE((short)6, "targetRole");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // START_TIME
          return START_TIME;
        case 2: // END_TIME
          return END_TIME;
        case 3: // ROLE
          return ROLE;
        case 4: // ORDER
          return ORDER;
        case 5: // META_ID
          return META_ID;
        case 6: // TARGET_ROLE
          return TARGET_ROLE;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __STARTTIME_ISSET_ID = 0;
  private static final int __ENDTIME_ISSET_ID = 1;
  private static final int __ORDER_ISSET_ID = 2;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.START_TIME,_Fields.END_TIME,_Fields.ROLE,_Fields.ORDER,_Fields.META_ID,_Fields.TARGET_ROLE};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.START_TIME, new org.apache.thrift.meta_data.FieldMetaData("startTime", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.END_TIME, new org.apache.thrift.meta_data.FieldMetaData("endTime", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ROLE, new org.apache.thrift.meta_data.FieldMetaData("role", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, PsRoleInfo.class)));
    tmpMap.put(_Fields.ORDER, new org.apache.thrift.meta_data.FieldMetaData("order", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.META_ID, new org.apache.thrift.meta_data.FieldMetaData("metaId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.TARGET_ROLE, new org.apache.thrift.meta_data.FieldMetaData("targetRole", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, PsRoleInfo.class)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PsOfficialHistory.class, metaDataMap);
  }

  public PsOfficialHistory() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PsOfficialHistory(PsOfficialHistory other) {
    __isset_bitfield = other.__isset_bitfield;
    this.startTime = other.startTime;
    this.endTime = other.endTime;
    if (other.isSetRole()) {
      this.role = new PsRoleInfo(other.role);
    }
    this.order = other.order;
    if (other.isSetMetaId()) {
      this.metaId = other.metaId;
    }
    if (other.isSetTargetRole()) {
      this.targetRole = new PsRoleInfo(other.targetRole);
    }
  }

  public PsOfficialHistory deepCopy() {
    return new PsOfficialHistory(this);
  }

  @Override
  public void clear() {
    setStartTimeIsSet(false);
    this.startTime = 0;
    setEndTimeIsSet(false);
    this.endTime = 0;
    this.role = null;
    setOrderIsSet(false);
    this.order = 0;
    this.metaId = null;
    this.targetRole = null;
  }

  /**
   * 开始时间戳 *
   */
  public long getStartTime() {
    return this.startTime;
  }

  /**
   * 开始时间戳 *
   */
  public PsOfficialHistory setStartTime(long startTime) {
    this.startTime = startTime;
    setStartTimeIsSet(true);
    return this;
  }

  public void unsetStartTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __STARTTIME_ISSET_ID);
  }

  /** Returns true if field startTime is set (has been assigned a value) and false otherwise */
  public boolean isSetStartTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __STARTTIME_ISSET_ID);
  }

  public void setStartTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __STARTTIME_ISSET_ID, value);
  }

  /**
   * 结束时间戳 *
   */
  public long getEndTime() {
    return this.endTime;
  }

  /**
   * 结束时间戳 *
   */
  public PsOfficialHistory setEndTime(long endTime) {
    this.endTime = endTime;
    setEndTimeIsSet(true);
    return this;
  }

  public void unsetEndTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ENDTIME_ISSET_ID);
  }

  /** Returns true if field endTime is set (has been assigned a value) and false otherwise */
  public boolean isSetEndTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ENDTIME_ISSET_ID);
  }

  public void setEndTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ENDTIME_ISSET_ID, value);
  }

  /**
   * 操作,官职玩家信息 *
   */
  @org.apache.thrift.annotation.Nullable
  public PsRoleInfo getRole() {
    return this.role;
  }

  /**
   * 操作,官职玩家信息 *
   */
  public PsOfficialHistory setRole(@org.apache.thrift.annotation.Nullable PsRoleInfo role) {
    this.role = role;
    return this;
  }

  public void unsetRole() {
    this.role = null;
  }

  /** Returns true if field role is set (has been assigned a value) and false otherwise */
  public boolean isSetRole() {
    return this.role != null;
  }

  public void setRoleIsSet(boolean value) {
    if (!value) {
      this.role = null;
    }
  }

  /**
   * 太子第几届 *
   */
  public int getOrder() {
    return this.order;
  }

  /**
   * 太子第几届 *
   */
  public PsOfficialHistory setOrder(int order) {
    this.order = order;
    setOrderIsSet(true);
    return this;
  }

  public void unsetOrder() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ORDER_ISSET_ID);
  }

  /** Returns true if field order is set (has been assigned a value) and false otherwise */
  public boolean isSetOrder() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ORDER_ISSET_ID);
  }

  public void setOrderIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ORDER_ISSET_ID, value);
  }

  /**
   * 嘉奖,技能配置id *
   */
  @org.apache.thrift.annotation.Nullable
  public java.lang.String getMetaId() {
    return this.metaId;
  }

  /**
   * 嘉奖,技能配置id *
   */
  public PsOfficialHistory setMetaId(@org.apache.thrift.annotation.Nullable java.lang.String metaId) {
    this.metaId = metaId;
    return this;
  }

  public void unsetMetaId() {
    this.metaId = null;
  }

  /** Returns true if field metaId is set (has been assigned a value) and false otherwise */
  public boolean isSetMetaId() {
    return this.metaId != null;
  }

  public void setMetaIdIsSet(boolean value) {
    if (!value) {
      this.metaId = null;
    }
  }

  /**
   * 嘉奖,技能的使用对象 *
   */
  @org.apache.thrift.annotation.Nullable
  public PsRoleInfo getTargetRole() {
    return this.targetRole;
  }

  /**
   * 嘉奖,技能的使用对象 *
   */
  public PsOfficialHistory setTargetRole(@org.apache.thrift.annotation.Nullable PsRoleInfo targetRole) {
    this.targetRole = targetRole;
    return this;
  }

  public void unsetTargetRole() {
    this.targetRole = null;
  }

  /** Returns true if field targetRole is set (has been assigned a value) and false otherwise */
  public boolean isSetTargetRole() {
    return this.targetRole != null;
  }

  public void setTargetRoleIsSet(boolean value) {
    if (!value) {
      this.targetRole = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case START_TIME:
      if (value == null) {
        unsetStartTime();
      } else {
        setStartTime((java.lang.Long)value);
      }
      break;

    case END_TIME:
      if (value == null) {
        unsetEndTime();
      } else {
        setEndTime((java.lang.Long)value);
      }
      break;

    case ROLE:
      if (value == null) {
        unsetRole();
      } else {
        setRole((PsRoleInfo)value);
      }
      break;

    case ORDER:
      if (value == null) {
        unsetOrder();
      } else {
        setOrder((java.lang.Integer)value);
      }
      break;

    case META_ID:
      if (value == null) {
        unsetMetaId();
      } else {
        setMetaId((java.lang.String)value);
      }
      break;

    case TARGET_ROLE:
      if (value == null) {
        unsetTargetRole();
      } else {
        setTargetRole((PsRoleInfo)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case START_TIME:
      return getStartTime();

    case END_TIME:
      return getEndTime();

    case ROLE:
      return getRole();

    case ORDER:
      return getOrder();

    case META_ID:
      return getMetaId();

    case TARGET_ROLE:
      return getTargetRole();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case START_TIME:
      return isSetStartTime();
    case END_TIME:
      return isSetEndTime();
    case ROLE:
      return isSetRole();
    case ORDER:
      return isSetOrder();
    case META_ID:
      return isSetMetaId();
    case TARGET_ROLE:
      return isSetTargetRole();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof PsOfficialHistory)
      return this.equals((PsOfficialHistory)that);
    return false;
  }

  public boolean equals(PsOfficialHistory that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_startTime = true && this.isSetStartTime();
    boolean that_present_startTime = true && that.isSetStartTime();
    if (this_present_startTime || that_present_startTime) {
      if (!(this_present_startTime && that_present_startTime))
        return false;
      if (this.startTime != that.startTime)
        return false;
    }

    boolean this_present_endTime = true && this.isSetEndTime();
    boolean that_present_endTime = true && that.isSetEndTime();
    if (this_present_endTime || that_present_endTime) {
      if (!(this_present_endTime && that_present_endTime))
        return false;
      if (this.endTime != that.endTime)
        return false;
    }

    boolean this_present_role = true && this.isSetRole();
    boolean that_present_role = true && that.isSetRole();
    if (this_present_role || that_present_role) {
      if (!(this_present_role && that_present_role))
        return false;
      if (!this.role.equals(that.role))
        return false;
    }

    boolean this_present_order = true && this.isSetOrder();
    boolean that_present_order = true && that.isSetOrder();
    if (this_present_order || that_present_order) {
      if (!(this_present_order && that_present_order))
        return false;
      if (this.order != that.order)
        return false;
    }

    boolean this_present_metaId = true && this.isSetMetaId();
    boolean that_present_metaId = true && that.isSetMetaId();
    if (this_present_metaId || that_present_metaId) {
      if (!(this_present_metaId && that_present_metaId))
        return false;
      if (!this.metaId.equals(that.metaId))
        return false;
    }

    boolean this_present_targetRole = true && this.isSetTargetRole();
    boolean that_present_targetRole = true && that.isSetTargetRole();
    if (this_present_targetRole || that_present_targetRole) {
      if (!(this_present_targetRole && that_present_targetRole))
        return false;
      if (!this.targetRole.equals(that.targetRole))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetStartTime()) ? 131071 : 524287);
    if (isSetStartTime())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(startTime);

    hashCode = hashCode * 8191 + ((isSetEndTime()) ? 131071 : 524287);
    if (isSetEndTime())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(endTime);

    hashCode = hashCode * 8191 + ((isSetRole()) ? 131071 : 524287);
    if (isSetRole())
      hashCode = hashCode * 8191 + role.hashCode();

    hashCode = hashCode * 8191 + ((isSetOrder()) ? 131071 : 524287);
    if (isSetOrder())
      hashCode = hashCode * 8191 + order;

    hashCode = hashCode * 8191 + ((isSetMetaId()) ? 131071 : 524287);
    if (isSetMetaId())
      hashCode = hashCode * 8191 + metaId.hashCode();

    hashCode = hashCode * 8191 + ((isSetTargetRole()) ? 131071 : 524287);
    if (isSetTargetRole())
      hashCode = hashCode * 8191 + targetRole.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(PsOfficialHistory other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetStartTime(), other.isSetStartTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStartTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.startTime, other.startTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetEndTime(), other.isSetEndTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetEndTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.endTime, other.endTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetRole(), other.isSetRole());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRole()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.role, other.role);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetOrder(), other.isSetOrder());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOrder()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.order, other.order);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetMetaId(), other.isSetMetaId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMetaId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.metaId, other.metaId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetTargetRole(), other.isSetTargetRole());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTargetRole()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.targetRole, other.targetRole);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("PsOfficialHistory(");
    boolean first = true;

    if (isSetStartTime()) {
      sb.append("startTime:");
      sb.append(this.startTime);
      first = false;
    }
    if (isSetEndTime()) {
      if (!first) sb.append(", ");
      sb.append("endTime:");
      sb.append(this.endTime);
      first = false;
    }
    if (isSetRole()) {
      if (!first) sb.append(", ");
      sb.append("role:");
      if (this.role == null) {
        sb.append("null");
      } else {
        sb.append(this.role);
      }
      first = false;
    }
    if (isSetOrder()) {
      if (!first) sb.append(", ");
      sb.append("order:");
      sb.append(this.order);
      first = false;
    }
    if (isSetMetaId()) {
      if (!first) sb.append(", ");
      sb.append("metaId:");
      if (this.metaId == null) {
        sb.append("null");
      } else {
        sb.append(this.metaId);
      }
      first = false;
    }
    if (isSetTargetRole()) {
      if (!first) sb.append(", ");
      sb.append("targetRole:");
      if (this.targetRole == null) {
        sb.append("null");
      } else {
        sb.append(this.targetRole);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
    if (role != null) {
      role.validate();
    }
    if (targetRole != null) {
      targetRole.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PsOfficialHistoryStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsOfficialHistoryStandardScheme getScheme() {
      return new PsOfficialHistoryStandardScheme();
    }
  }

  private static class PsOfficialHistoryStandardScheme extends org.apache.thrift.scheme.StandardScheme<PsOfficialHistory> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PsOfficialHistory struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // START_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.startTime = iprot.readI64();
              struct.setStartTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // END_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.endTime = iprot.readI64();
              struct.setEndTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // ROLE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.role = new PsRoleInfo();
              struct.role.read(iprot);
              struct.setRoleIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // ORDER
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.order = iprot.readI32();
              struct.setOrderIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // META_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.metaId = iprot.readString();
              struct.setMetaIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // TARGET_ROLE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.targetRole = new PsRoleInfo();
              struct.targetRole.read(iprot);
              struct.setTargetRoleIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PsOfficialHistory struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.isSetStartTime()) {
        oprot.writeFieldBegin(START_TIME_FIELD_DESC);
        oprot.writeI64(struct.startTime);
        oprot.writeFieldEnd();
      }
      if (struct.isSetEndTime()) {
        oprot.writeFieldBegin(END_TIME_FIELD_DESC);
        oprot.writeI64(struct.endTime);
        oprot.writeFieldEnd();
      }
      if (struct.role != null) {
        if (struct.isSetRole()) {
          oprot.writeFieldBegin(ROLE_FIELD_DESC);
          struct.role.write(oprot);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetOrder()) {
        oprot.writeFieldBegin(ORDER_FIELD_DESC);
        oprot.writeI32(struct.order);
        oprot.writeFieldEnd();
      }
      if (struct.metaId != null) {
        if (struct.isSetMetaId()) {
          oprot.writeFieldBegin(META_ID_FIELD_DESC);
          oprot.writeString(struct.metaId);
          oprot.writeFieldEnd();
        }
      }
      if (struct.targetRole != null) {
        if (struct.isSetTargetRole()) {
          oprot.writeFieldBegin(TARGET_ROLE_FIELD_DESC);
          struct.targetRole.write(oprot);
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PsOfficialHistoryTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsOfficialHistoryTupleScheme getScheme() {
      return new PsOfficialHistoryTupleScheme();
    }
  }

  private static class PsOfficialHistoryTupleScheme extends org.apache.thrift.scheme.TupleScheme<PsOfficialHistory> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PsOfficialHistory struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetStartTime()) {
        optionals.set(0);
      }
      if (struct.isSetEndTime()) {
        optionals.set(1);
      }
      if (struct.isSetRole()) {
        optionals.set(2);
      }
      if (struct.isSetOrder()) {
        optionals.set(3);
      }
      if (struct.isSetMetaId()) {
        optionals.set(4);
      }
      if (struct.isSetTargetRole()) {
        optionals.set(5);
      }
      oprot.writeBitSet(optionals, 6);
      if (struct.isSetStartTime()) {
        oprot.writeI64(struct.startTime);
      }
      if (struct.isSetEndTime()) {
        oprot.writeI64(struct.endTime);
      }
      if (struct.isSetRole()) {
        struct.role.write(oprot);
      }
      if (struct.isSetOrder()) {
        oprot.writeI32(struct.order);
      }
      if (struct.isSetMetaId()) {
        oprot.writeString(struct.metaId);
      }
      if (struct.isSetTargetRole()) {
        struct.targetRole.write(oprot);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PsOfficialHistory struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(6);
      if (incoming.get(0)) {
        struct.startTime = iprot.readI64();
        struct.setStartTimeIsSet(true);
      }
      if (incoming.get(1)) {
        struct.endTime = iprot.readI64();
        struct.setEndTimeIsSet(true);
      }
      if (incoming.get(2)) {
        struct.role = new PsRoleInfo();
        struct.role.read(iprot);
        struct.setRoleIsSet(true);
      }
      if (incoming.get(3)) {
        struct.order = iprot.readI32();
        struct.setOrderIsSet(true);
      }
      if (incoming.get(4)) {
        struct.metaId = iprot.readString();
        struct.setMetaIdIsSet(true);
      }
      if (incoming.get(5)) {
        struct.targetRole = new PsRoleInfo();
        struct.targetRole.read(iprot);
        struct.setTargetRoleIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

