/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.structure;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 科技信息
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class PsAllianceTechInfo implements org.apache.thrift.TBase<PsAllianceTechInfo, PsAllianceTechInfo._Fields>, java.io.Serializable, Cloneable, Comparable<PsAllianceTechInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PsAllianceTechInfo");

  private static final org.apache.thrift.protocol.TField GROUP_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("groupId", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField DONATE_COUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("donateCount", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField LEVEL_FIELD_DESC = new org.apache.thrift.protocol.TField("level", org.apache.thrift.protocol.TType.I32, (short)3);
  private static final org.apache.thrift.protocol.TField TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("time", org.apache.thrift.protocol.TType.I64, (short)4);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new PsAllianceTechInfoStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new PsAllianceTechInfoTupleSchemeFactory();

  /**
   * group id
   */
  public int groupId; // required
  /**
   * 捐献数量
   */
  public int donateCount; // required
  /**
   * 等级
   */
  public int level; // required
  /**
   * 升级结束时间
   */
  public long time; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * group id
     */
    GROUP_ID((short)1, "groupId"),
    /**
     * 捐献数量
     */
    DONATE_COUNT((short)2, "donateCount"),
    /**
     * 等级
     */
    LEVEL((short)3, "level"),
    /**
     * 升级结束时间
     */
    TIME((short)4, "time");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // GROUP_ID
          return GROUP_ID;
        case 2: // DONATE_COUNT
          return DONATE_COUNT;
        case 3: // LEVEL
          return LEVEL;
        case 4: // TIME
          return TIME;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __GROUPID_ISSET_ID = 0;
  private static final int __DONATECOUNT_ISSET_ID = 1;
  private static final int __LEVEL_ISSET_ID = 2;
  private static final int __TIME_ISSET_ID = 3;
  private byte __isset_bitfield = 0;
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.GROUP_ID, new org.apache.thrift.meta_data.FieldMetaData("groupId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.DONATE_COUNT, new org.apache.thrift.meta_data.FieldMetaData("donateCount", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.LEVEL, new org.apache.thrift.meta_data.FieldMetaData("level", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.TIME, new org.apache.thrift.meta_data.FieldMetaData("time", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PsAllianceTechInfo.class, metaDataMap);
  }

  public PsAllianceTechInfo() {
  }

  public PsAllianceTechInfo(
    int groupId,
    int donateCount,
    int level,
    long time)
  {
    this();
    this.groupId = groupId;
    setGroupIdIsSet(true);
    this.donateCount = donateCount;
    setDonateCountIsSet(true);
    this.level = level;
    setLevelIsSet(true);
    this.time = time;
    setTimeIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PsAllianceTechInfo(PsAllianceTechInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    this.groupId = other.groupId;
    this.donateCount = other.donateCount;
    this.level = other.level;
    this.time = other.time;
  }

  public PsAllianceTechInfo deepCopy() {
    return new PsAllianceTechInfo(this);
  }

  @Override
  public void clear() {
    setGroupIdIsSet(false);
    this.groupId = 0;
    setDonateCountIsSet(false);
    this.donateCount = 0;
    setLevelIsSet(false);
    this.level = 0;
    setTimeIsSet(false);
    this.time = 0;
  }

  /**
   * group id
   */
  public int getGroupId() {
    return this.groupId;
  }

  /**
   * group id
   */
  public PsAllianceTechInfo setGroupId(int groupId) {
    this.groupId = groupId;
    setGroupIdIsSet(true);
    return this;
  }

  public void unsetGroupId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __GROUPID_ISSET_ID);
  }

  /** Returns true if field groupId is set (has been assigned a value) and false otherwise */
  public boolean isSetGroupId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __GROUPID_ISSET_ID);
  }

  public void setGroupIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __GROUPID_ISSET_ID, value);
  }

  /**
   * 捐献数量
   */
  public int getDonateCount() {
    return this.donateCount;
  }

  /**
   * 捐献数量
   */
  public PsAllianceTechInfo setDonateCount(int donateCount) {
    this.donateCount = donateCount;
    setDonateCountIsSet(true);
    return this;
  }

  public void unsetDonateCount() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __DONATECOUNT_ISSET_ID);
  }

  /** Returns true if field donateCount is set (has been assigned a value) and false otherwise */
  public boolean isSetDonateCount() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __DONATECOUNT_ISSET_ID);
  }

  public void setDonateCountIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __DONATECOUNT_ISSET_ID, value);
  }

  /**
   * 等级
   */
  public int getLevel() {
    return this.level;
  }

  /**
   * 等级
   */
  public PsAllianceTechInfo setLevel(int level) {
    this.level = level;
    setLevelIsSet(true);
    return this;
  }

  public void unsetLevel() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __LEVEL_ISSET_ID);
  }

  /** Returns true if field level is set (has been assigned a value) and false otherwise */
  public boolean isSetLevel() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __LEVEL_ISSET_ID);
  }

  public void setLevelIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __LEVEL_ISSET_ID, value);
  }

  /**
   * 升级结束时间
   */
  public long getTime() {
    return this.time;
  }

  /**
   * 升级结束时间
   */
  public PsAllianceTechInfo setTime(long time) {
    this.time = time;
    setTimeIsSet(true);
    return this;
  }

  public void unsetTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __TIME_ISSET_ID);
  }

  /** Returns true if field time is set (has been assigned a value) and false otherwise */
  public boolean isSetTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __TIME_ISSET_ID);
  }

  public void setTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __TIME_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case GROUP_ID:
      if (value == null) {
        unsetGroupId();
      } else {
        setGroupId((java.lang.Integer)value);
      }
      break;

    case DONATE_COUNT:
      if (value == null) {
        unsetDonateCount();
      } else {
        setDonateCount((java.lang.Integer)value);
      }
      break;

    case LEVEL:
      if (value == null) {
        unsetLevel();
      } else {
        setLevel((java.lang.Integer)value);
      }
      break;

    case TIME:
      if (value == null) {
        unsetTime();
      } else {
        setTime((java.lang.Long)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case GROUP_ID:
      return getGroupId();

    case DONATE_COUNT:
      return getDonateCount();

    case LEVEL:
      return getLevel();

    case TIME:
      return getTime();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case GROUP_ID:
      return isSetGroupId();
    case DONATE_COUNT:
      return isSetDonateCount();
    case LEVEL:
      return isSetLevel();
    case TIME:
      return isSetTime();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof PsAllianceTechInfo)
      return this.equals((PsAllianceTechInfo)that);
    return false;
  }

  public boolean equals(PsAllianceTechInfo that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_groupId = true;
    boolean that_present_groupId = true;
    if (this_present_groupId || that_present_groupId) {
      if (!(this_present_groupId && that_present_groupId))
        return false;
      if (this.groupId != that.groupId)
        return false;
    }

    boolean this_present_donateCount = true;
    boolean that_present_donateCount = true;
    if (this_present_donateCount || that_present_donateCount) {
      if (!(this_present_donateCount && that_present_donateCount))
        return false;
      if (this.donateCount != that.donateCount)
        return false;
    }

    boolean this_present_level = true;
    boolean that_present_level = true;
    if (this_present_level || that_present_level) {
      if (!(this_present_level && that_present_level))
        return false;
      if (this.level != that.level)
        return false;
    }

    boolean this_present_time = true;
    boolean that_present_time = true;
    if (this_present_time || that_present_time) {
      if (!(this_present_time && that_present_time))
        return false;
      if (this.time != that.time)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + groupId;

    hashCode = hashCode * 8191 + donateCount;

    hashCode = hashCode * 8191 + level;

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(time);

    return hashCode;
  }

  @Override
  public int compareTo(PsAllianceTechInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetGroupId(), other.isSetGroupId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGroupId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.groupId, other.groupId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetDonateCount(), other.isSetDonateCount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDonateCount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.donateCount, other.donateCount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetLevel(), other.isSetLevel());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetLevel()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.level, other.level);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetTime(), other.isSetTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.time, other.time);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("PsAllianceTechInfo(");
    boolean first = true;

    sb.append("groupId:");
    sb.append(this.groupId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("donateCount:");
    sb.append(this.donateCount);
    first = false;
    if (!first) sb.append(", ");
    sb.append("level:");
    sb.append(this.level);
    first = false;
    if (!first) sb.append(", ");
    sb.append("time:");
    sb.append(this.time);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'groupId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'donateCount' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'level' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'time' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PsAllianceTechInfoStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsAllianceTechInfoStandardScheme getScheme() {
      return new PsAllianceTechInfoStandardScheme();
    }
  }

  private static class PsAllianceTechInfoStandardScheme extends org.apache.thrift.scheme.StandardScheme<PsAllianceTechInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PsAllianceTechInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // GROUP_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.groupId = iprot.readI32();
              struct.setGroupIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // DONATE_COUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.donateCount = iprot.readI32();
              struct.setDonateCountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // LEVEL
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.level = iprot.readI32();
              struct.setLevelIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.time = iprot.readI64();
              struct.setTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetGroupId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'groupId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetDonateCount()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'donateCount' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetLevel()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'level' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetTime()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'time' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PsAllianceTechInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(GROUP_ID_FIELD_DESC);
      oprot.writeI32(struct.groupId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(DONATE_COUNT_FIELD_DESC);
      oprot.writeI32(struct.donateCount);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(LEVEL_FIELD_DESC);
      oprot.writeI32(struct.level);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(TIME_FIELD_DESC);
      oprot.writeI64(struct.time);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PsAllianceTechInfoTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsAllianceTechInfoTupleScheme getScheme() {
      return new PsAllianceTechInfoTupleScheme();
    }
  }

  private static class PsAllianceTechInfoTupleScheme extends org.apache.thrift.scheme.TupleScheme<PsAllianceTechInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PsAllianceTechInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI32(struct.groupId);
      oprot.writeI32(struct.donateCount);
      oprot.writeI32(struct.level);
      oprot.writeI64(struct.time);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PsAllianceTechInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.groupId = iprot.readI32();
      struct.setGroupIdIsSet(true);
      struct.donateCount = iprot.readI32();
      struct.setDonateCountIsSet(true);
      struct.level = iprot.readI32();
      struct.setLevelIsSet(true);
      struct.time = iprot.readI64();
      struct.setTimeIsSet(true);
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

