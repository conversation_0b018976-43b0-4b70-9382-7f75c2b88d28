/**
 * Autogenerated by Thrift Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.constant;


/**
 * Bingo格式状态
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public enum PsBingoCellStauts implements org.apache.thrift.TEnum {
  /**
   * 默认 未打开
   */
  DEFAULT(1),
  /**
   * 打开
   */
  OPEN(2),
  /**
   * bingo
   */
  BINGO(3),
  /**
   * 万能格子
   */
  UNLIMIT(4),
  /**
   * 锁定格子
   */
  LOCK(5),
  /**
   * bingo完，变成黑格子
   */
  BLACK(6),
  /**
   * 中奖数字
   */
  PRIZE(7);

  private final int value;

  private PsBingoCellStauts(int value) {
    this.value = value;
  }

  /**
   * Get the integer value of this enum value, as defined in the Thrift IDL.
   */
  public int getValue() {
    return value;
  }

  /**
   * Find a the enum type by its integer value, as defined in the Thrift IDL.
   * @return null if the value is not found.
   */
  @org.apache.thrift.annotation.Nullable
  public static PsBingoCellStauts findByValue(int value) { 
    switch (value) {
      case 1:
        return DEFAULT;
      case 2:
        return OPEN;
      case 3:
        return BINGO;
      case 4:
        return UNLIMIT;
      case 5:
        return LOCK;
      case 6:
        return BLACK;
      case 7:
        return PRIZE;
      default:
        return null;
    }
  }
}
