/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.structure;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 跨服抢城积分信息
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class PsCSARolePoint implements org.apache.thrift.TBase<PsCSARolePoint, PsCSARolePoint._Fields>, java.io.Serializable, Cloneable, Comparable<PsCSARolePoint> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PsCSARolePoint");

  private static final org.apache.thrift.protocol.TField ROLE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("roleId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField POINT_FIELD_DESC = new org.apache.thrift.protocol.TField("point", org.apache.thrift.protocol.TType.I64, (short)2);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new PsCSARolePointStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new PsCSARolePointTupleSchemeFactory();

  /**
   * 玩家Id *
   */
  public long roleId; // optional
  /**
   * 联盟点数 *
   */
  public long point; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 玩家Id *
     */
    ROLE_ID((short)1, "roleId"),
    /**
     * 联盟点数 *
     */
    POINT((short)2, "point");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ROLE_ID
          return ROLE_ID;
        case 2: // POINT
          return POINT;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ROLEID_ISSET_ID = 0;
  private static final int __POINT_ISSET_ID = 1;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.ROLE_ID,_Fields.POINT};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ROLE_ID, new org.apache.thrift.meta_data.FieldMetaData("roleId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.POINT, new org.apache.thrift.meta_data.FieldMetaData("point", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PsCSARolePoint.class, metaDataMap);
  }

  public PsCSARolePoint() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PsCSARolePoint(PsCSARolePoint other) {
    __isset_bitfield = other.__isset_bitfield;
    this.roleId = other.roleId;
    this.point = other.point;
  }

  public PsCSARolePoint deepCopy() {
    return new PsCSARolePoint(this);
  }

  @Override
  public void clear() {
    setRoleIdIsSet(false);
    this.roleId = 0;
    setPointIsSet(false);
    this.point = 0;
  }

  /**
   * 玩家Id *
   */
  public long getRoleId() {
    return this.roleId;
  }

  /**
   * 玩家Id *
   */
  public PsCSARolePoint setRoleId(long roleId) {
    this.roleId = roleId;
    setRoleIdIsSet(true);
    return this;
  }

  public void unsetRoleId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ROLEID_ISSET_ID);
  }

  /** Returns true if field roleId is set (has been assigned a value) and false otherwise */
  public boolean isSetRoleId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ROLEID_ISSET_ID);
  }

  public void setRoleIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ROLEID_ISSET_ID, value);
  }

  /**
   * 联盟点数 *
   */
  public long getPoint() {
    return this.point;
  }

  /**
   * 联盟点数 *
   */
  public PsCSARolePoint setPoint(long point) {
    this.point = point;
    setPointIsSet(true);
    return this;
  }

  public void unsetPoint() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __POINT_ISSET_ID);
  }

  /** Returns true if field point is set (has been assigned a value) and false otherwise */
  public boolean isSetPoint() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __POINT_ISSET_ID);
  }

  public void setPointIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __POINT_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ROLE_ID:
      if (value == null) {
        unsetRoleId();
      } else {
        setRoleId((java.lang.Long)value);
      }
      break;

    case POINT:
      if (value == null) {
        unsetPoint();
      } else {
        setPoint((java.lang.Long)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ROLE_ID:
      return getRoleId();

    case POINT:
      return getPoint();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ROLE_ID:
      return isSetRoleId();
    case POINT:
      return isSetPoint();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof PsCSARolePoint)
      return this.equals((PsCSARolePoint)that);
    return false;
  }

  public boolean equals(PsCSARolePoint that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_roleId = true && this.isSetRoleId();
    boolean that_present_roleId = true && that.isSetRoleId();
    if (this_present_roleId || that_present_roleId) {
      if (!(this_present_roleId && that_present_roleId))
        return false;
      if (this.roleId != that.roleId)
        return false;
    }

    boolean this_present_point = true && this.isSetPoint();
    boolean that_present_point = true && that.isSetPoint();
    if (this_present_point || that_present_point) {
      if (!(this_present_point && that_present_point))
        return false;
      if (this.point != that.point)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetRoleId()) ? 131071 : 524287);
    if (isSetRoleId())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(roleId);

    hashCode = hashCode * 8191 + ((isSetPoint()) ? 131071 : 524287);
    if (isSetPoint())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(point);

    return hashCode;
  }

  @Override
  public int compareTo(PsCSARolePoint other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetRoleId(), other.isSetRoleId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoleId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roleId, other.roleId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetPoint(), other.isSetPoint());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPoint()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.point, other.point);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("PsCSARolePoint(");
    boolean first = true;

    if (isSetRoleId()) {
      sb.append("roleId:");
      sb.append(this.roleId);
      first = false;
    }
    if (isSetPoint()) {
      if (!first) sb.append(", ");
      sb.append("point:");
      sb.append(this.point);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PsCSARolePointStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsCSARolePointStandardScheme getScheme() {
      return new PsCSARolePointStandardScheme();
    }
  }

  private static class PsCSARolePointStandardScheme extends org.apache.thrift.scheme.StandardScheme<PsCSARolePoint> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PsCSARolePoint struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ROLE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.roleId = iprot.readI64();
              struct.setRoleIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // POINT
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.point = iprot.readI64();
              struct.setPointIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PsCSARolePoint struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.isSetRoleId()) {
        oprot.writeFieldBegin(ROLE_ID_FIELD_DESC);
        oprot.writeI64(struct.roleId);
        oprot.writeFieldEnd();
      }
      if (struct.isSetPoint()) {
        oprot.writeFieldBegin(POINT_FIELD_DESC);
        oprot.writeI64(struct.point);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PsCSARolePointTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsCSARolePointTupleScheme getScheme() {
      return new PsCSARolePointTupleScheme();
    }
  }

  private static class PsCSARolePointTupleScheme extends org.apache.thrift.scheme.TupleScheme<PsCSARolePoint> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PsCSARolePoint struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetRoleId()) {
        optionals.set(0);
      }
      if (struct.isSetPoint()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetRoleId()) {
        oprot.writeI64(struct.roleId);
      }
      if (struct.isSetPoint()) {
        oprot.writeI64(struct.point);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PsCSARolePoint struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        struct.roleId = iprot.readI64();
        struct.setRoleIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.point = iprot.readI64();
        struct.setPointIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

