/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 新增一个事件
 * @Message(5035)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcAddPlayerWorldExploreEvent implements org.apache.thrift.TBase<GcAddPlayerWorldExploreEvent, GcAddPlayerWorldExploreEvent._Fields>, java.io.Serializable, Cloneable, Comparable<GcAddPlayerWorldExploreEvent> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcAddPlayerWorldExploreEvent");

  private static final org.apache.thrift.protocol.TField EVENT_FIELD_DESC = new org.apache.thrift.protocol.TField("event", org.apache.thrift.protocol.TType.STRUCT, (short)1);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcAddPlayerWorldExploreEventStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcAddPlayerWorldExploreEventTupleSchemeFactory();

  /**
   * 事件实体
   */
  public @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsPlayerWorldExploreEvent event; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 事件实体
     */
    EVENT((short)1, "event");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // EVENT
          return EVENT;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final _Fields optionals[] = {_Fields.EVENT};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.EVENT, new org.apache.thrift.meta_data.FieldMetaData("event", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsPlayerWorldExploreEvent.class)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcAddPlayerWorldExploreEvent.class, metaDataMap);
  }

  public GcAddPlayerWorldExploreEvent() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcAddPlayerWorldExploreEvent(GcAddPlayerWorldExploreEvent other) {
    if (other.isSetEvent()) {
      this.event = new com.lc.billion.icefire.protocol.structure.PsPlayerWorldExploreEvent(other.event);
    }
  }

  public GcAddPlayerWorldExploreEvent deepCopy() {
    return new GcAddPlayerWorldExploreEvent(this);
  }

  @Override
  public void clear() {
    this.event = null;
  }

  /**
   * 事件实体
   */
  @org.apache.thrift.annotation.Nullable
  public com.lc.billion.icefire.protocol.structure.PsPlayerWorldExploreEvent getEvent() {
    return this.event;
  }

  /**
   * 事件实体
   */
  public GcAddPlayerWorldExploreEvent setEvent(@org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsPlayerWorldExploreEvent event) {
    this.event = event;
    return this;
  }

  public void unsetEvent() {
    this.event = null;
  }

  /** Returns true if field event is set (has been assigned a value) and false otherwise */
  public boolean isSetEvent() {
    return this.event != null;
  }

  public void setEventIsSet(boolean value) {
    if (!value) {
      this.event = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case EVENT:
      if (value == null) {
        unsetEvent();
      } else {
        setEvent((com.lc.billion.icefire.protocol.structure.PsPlayerWorldExploreEvent)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case EVENT:
      return getEvent();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case EVENT:
      return isSetEvent();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcAddPlayerWorldExploreEvent)
      return this.equals((GcAddPlayerWorldExploreEvent)that);
    return false;
  }

  public boolean equals(GcAddPlayerWorldExploreEvent that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_event = true && this.isSetEvent();
    boolean that_present_event = true && that.isSetEvent();
    if (this_present_event || that_present_event) {
      if (!(this_present_event && that_present_event))
        return false;
      if (!this.event.equals(that.event))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetEvent()) ? 131071 : 524287);
    if (isSetEvent())
      hashCode = hashCode * 8191 + event.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(GcAddPlayerWorldExploreEvent other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetEvent(), other.isSetEvent());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetEvent()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.event, other.event);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcAddPlayerWorldExploreEvent(");
    boolean first = true;

    if (isSetEvent()) {
      sb.append("event:");
      if (this.event == null) {
        sb.append("null");
      } else {
        sb.append(this.event);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
    if (event != null) {
      event.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcAddPlayerWorldExploreEventStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcAddPlayerWorldExploreEventStandardScheme getScheme() {
      return new GcAddPlayerWorldExploreEventStandardScheme();
    }
  }

  private static class GcAddPlayerWorldExploreEventStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcAddPlayerWorldExploreEvent> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcAddPlayerWorldExploreEvent struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // EVENT
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.event = new com.lc.billion.icefire.protocol.structure.PsPlayerWorldExploreEvent();
              struct.event.read(iprot);
              struct.setEventIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcAddPlayerWorldExploreEvent struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.event != null) {
        if (struct.isSetEvent()) {
          oprot.writeFieldBegin(EVENT_FIELD_DESC);
          struct.event.write(oprot);
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcAddPlayerWorldExploreEventTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcAddPlayerWorldExploreEventTupleScheme getScheme() {
      return new GcAddPlayerWorldExploreEventTupleScheme();
    }
  }

  private static class GcAddPlayerWorldExploreEventTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcAddPlayerWorldExploreEvent> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcAddPlayerWorldExploreEvent struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetEvent()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetEvent()) {
        struct.event.write(oprot);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcAddPlayerWorldExploreEvent struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        struct.event = new com.lc.billion.icefire.protocol.structure.PsPlayerWorldExploreEvent();
        struct.event.read(iprot);
        struct.setEventIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

