/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 邀请一个人进入联盟
 * @Message(906)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcAllianceInviteOne implements org.apache.thrift.TBase<GcAllianceInviteOne, GcAllianceInviteOne._Fields>, java.io.Serializable, Cloneable, Comparable<GcAllianceInviteOne> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcAllianceInviteOne");

  private static final org.apache.thrift.protocol.TField ERROR_CODE_FIELD_DESC = new org.apache.thrift.protocol.TField("errorCode", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField CAN_ATTEND_ALLIANCE_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("canAttendAllianceTime", org.apache.thrift.protocol.TType.I64, (short)2);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcAllianceInviteOneStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcAllianceInviteOneTupleSchemeFactory();

  /**
   * 0 成功
   * 1 重复邀请
   * 2 已经有联盟
   * 3 被邀请玩家是自己盟
   * 4 不同服
   * 5 cd中
   */
  public int errorCode; // required
  public long canAttendAllianceTime; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 0 成功
     * 1 重复邀请
     * 2 已经有联盟
     * 3 被邀请玩家是自己盟
     * 4 不同服
     * 5 cd中
     */
    ERROR_CODE((short)1, "errorCode"),
    CAN_ATTEND_ALLIANCE_TIME((short)2, "canAttendAllianceTime");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ERROR_CODE
          return ERROR_CODE;
        case 2: // CAN_ATTEND_ALLIANCE_TIME
          return CAN_ATTEND_ALLIANCE_TIME;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ERRORCODE_ISSET_ID = 0;
  private static final int __CANATTENDALLIANCETIME_ISSET_ID = 1;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.CAN_ATTEND_ALLIANCE_TIME};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ERROR_CODE, new org.apache.thrift.meta_data.FieldMetaData("errorCode", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.CAN_ATTEND_ALLIANCE_TIME, new org.apache.thrift.meta_data.FieldMetaData("canAttendAllianceTime", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcAllianceInviteOne.class, metaDataMap);
  }

  public GcAllianceInviteOne() {
  }

  public GcAllianceInviteOne(
    int errorCode)
  {
    this();
    this.errorCode = errorCode;
    setErrorCodeIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcAllianceInviteOne(GcAllianceInviteOne other) {
    __isset_bitfield = other.__isset_bitfield;
    this.errorCode = other.errorCode;
    this.canAttendAllianceTime = other.canAttendAllianceTime;
  }

  public GcAllianceInviteOne deepCopy() {
    return new GcAllianceInviteOne(this);
  }

  @Override
  public void clear() {
    setErrorCodeIsSet(false);
    this.errorCode = 0;
    setCanAttendAllianceTimeIsSet(false);
    this.canAttendAllianceTime = 0;
  }

  /**
   * 0 成功
   * 1 重复邀请
   * 2 已经有联盟
   * 3 被邀请玩家是自己盟
   * 4 不同服
   * 5 cd中
   */
  public int getErrorCode() {
    return this.errorCode;
  }

  /**
   * 0 成功
   * 1 重复邀请
   * 2 已经有联盟
   * 3 被邀请玩家是自己盟
   * 4 不同服
   * 5 cd中
   */
  public GcAllianceInviteOne setErrorCode(int errorCode) {
    this.errorCode = errorCode;
    setErrorCodeIsSet(true);
    return this;
  }

  public void unsetErrorCode() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ERRORCODE_ISSET_ID);
  }

  /** Returns true if field errorCode is set (has been assigned a value) and false otherwise */
  public boolean isSetErrorCode() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ERRORCODE_ISSET_ID);
  }

  public void setErrorCodeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ERRORCODE_ISSET_ID, value);
  }

  public long getCanAttendAllianceTime() {
    return this.canAttendAllianceTime;
  }

  public GcAllianceInviteOne setCanAttendAllianceTime(long canAttendAllianceTime) {
    this.canAttendAllianceTime = canAttendAllianceTime;
    setCanAttendAllianceTimeIsSet(true);
    return this;
  }

  public void unsetCanAttendAllianceTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __CANATTENDALLIANCETIME_ISSET_ID);
  }

  /** Returns true if field canAttendAllianceTime is set (has been assigned a value) and false otherwise */
  public boolean isSetCanAttendAllianceTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __CANATTENDALLIANCETIME_ISSET_ID);
  }

  public void setCanAttendAllianceTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __CANATTENDALLIANCETIME_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ERROR_CODE:
      if (value == null) {
        unsetErrorCode();
      } else {
        setErrorCode((java.lang.Integer)value);
      }
      break;

    case CAN_ATTEND_ALLIANCE_TIME:
      if (value == null) {
        unsetCanAttendAllianceTime();
      } else {
        setCanAttendAllianceTime((java.lang.Long)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ERROR_CODE:
      return getErrorCode();

    case CAN_ATTEND_ALLIANCE_TIME:
      return getCanAttendAllianceTime();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ERROR_CODE:
      return isSetErrorCode();
    case CAN_ATTEND_ALLIANCE_TIME:
      return isSetCanAttendAllianceTime();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcAllianceInviteOne)
      return this.equals((GcAllianceInviteOne)that);
    return false;
  }

  public boolean equals(GcAllianceInviteOne that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_errorCode = true;
    boolean that_present_errorCode = true;
    if (this_present_errorCode || that_present_errorCode) {
      if (!(this_present_errorCode && that_present_errorCode))
        return false;
      if (this.errorCode != that.errorCode)
        return false;
    }

    boolean this_present_canAttendAllianceTime = true && this.isSetCanAttendAllianceTime();
    boolean that_present_canAttendAllianceTime = true && that.isSetCanAttendAllianceTime();
    if (this_present_canAttendAllianceTime || that_present_canAttendAllianceTime) {
      if (!(this_present_canAttendAllianceTime && that_present_canAttendAllianceTime))
        return false;
      if (this.canAttendAllianceTime != that.canAttendAllianceTime)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + errorCode;

    hashCode = hashCode * 8191 + ((isSetCanAttendAllianceTime()) ? 131071 : 524287);
    if (isSetCanAttendAllianceTime())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(canAttendAllianceTime);

    return hashCode;
  }

  @Override
  public int compareTo(GcAllianceInviteOne other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetErrorCode(), other.isSetErrorCode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetErrorCode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.errorCode, other.errorCode);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetCanAttendAllianceTime(), other.isSetCanAttendAllianceTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCanAttendAllianceTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.canAttendAllianceTime, other.canAttendAllianceTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcAllianceInviteOne(");
    boolean first = true;

    sb.append("errorCode:");
    sb.append(this.errorCode);
    first = false;
    if (isSetCanAttendAllianceTime()) {
      if (!first) sb.append(", ");
      sb.append("canAttendAllianceTime:");
      sb.append(this.canAttendAllianceTime);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'errorCode' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcAllianceInviteOneStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcAllianceInviteOneStandardScheme getScheme() {
      return new GcAllianceInviteOneStandardScheme();
    }
  }

  private static class GcAllianceInviteOneStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcAllianceInviteOne> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcAllianceInviteOne struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ERROR_CODE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.errorCode = iprot.readI32();
              struct.setErrorCodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // CAN_ATTEND_ALLIANCE_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.canAttendAllianceTime = iprot.readI64();
              struct.setCanAttendAllianceTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetErrorCode()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'errorCode' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcAllianceInviteOne struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ERROR_CODE_FIELD_DESC);
      oprot.writeI32(struct.errorCode);
      oprot.writeFieldEnd();
      if (struct.isSetCanAttendAllianceTime()) {
        oprot.writeFieldBegin(CAN_ATTEND_ALLIANCE_TIME_FIELD_DESC);
        oprot.writeI64(struct.canAttendAllianceTime);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcAllianceInviteOneTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcAllianceInviteOneTupleScheme getScheme() {
      return new GcAllianceInviteOneTupleScheme();
    }
  }

  private static class GcAllianceInviteOneTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcAllianceInviteOne> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcAllianceInviteOne struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI32(struct.errorCode);
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetCanAttendAllianceTime()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetCanAttendAllianceTime()) {
        oprot.writeI64(struct.canAttendAllianceTime);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcAllianceInviteOne struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.errorCode = iprot.readI32();
      struct.setErrorCodeIsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        struct.canAttendAllianceTime = iprot.readI64();
        struct.setCanAttendAllianceTimeIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

