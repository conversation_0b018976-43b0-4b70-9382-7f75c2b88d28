/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 炮台历史记录返回
 * @Message(6933)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcRegionCapitalArtilleryHistory implements org.apache.thrift.TBase<GcRegionCapitalArtilleryHistory, GcRegionCapitalArtilleryHistory._Fields>, java.io.Serializable, Cloneable, Comparable<GcRegionCapitalArtilleryHistory> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcRegionCapitalArtilleryHistory");

  private static final org.apache.thrift.protocol.TField OPT_LIST_FIELD_DESC = new org.apache.thrift.protocol.TField("optList", org.apache.thrift.protocol.TType.LIST, (short)2);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcRegionCapitalArtilleryHistoryStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcRegionCapitalArtilleryHistoryTupleSchemeFactory();

  /**
   * 占领记录
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsArtilleryRecord> optList; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 占领记录
     */
    OPT_LIST((short)2, "optList");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 2: // OPT_LIST
          return OPT_LIST;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final _Fields optionals[] = {_Fields.OPT_LIST};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.OPT_LIST, new org.apache.thrift.meta_data.FieldMetaData("optList", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsArtilleryRecord.class))));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcRegionCapitalArtilleryHistory.class, metaDataMap);
  }

  public GcRegionCapitalArtilleryHistory() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcRegionCapitalArtilleryHistory(GcRegionCapitalArtilleryHistory other) {
    if (other.isSetOptList()) {
      java.util.List<com.lc.billion.icefire.protocol.structure.PsArtilleryRecord> __this__optList = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsArtilleryRecord>(other.optList.size());
      for (com.lc.billion.icefire.protocol.structure.PsArtilleryRecord other_element : other.optList) {
        __this__optList.add(new com.lc.billion.icefire.protocol.structure.PsArtilleryRecord(other_element));
      }
      this.optList = __this__optList;
    }
  }

  public GcRegionCapitalArtilleryHistory deepCopy() {
    return new GcRegionCapitalArtilleryHistory(this);
  }

  @Override
  public void clear() {
    this.optList = null;
  }

  public int getOptListSize() {
    return (this.optList == null) ? 0 : this.optList.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<com.lc.billion.icefire.protocol.structure.PsArtilleryRecord> getOptListIterator() {
    return (this.optList == null) ? null : this.optList.iterator();
  }

  public void addToOptList(com.lc.billion.icefire.protocol.structure.PsArtilleryRecord elem) {
    if (this.optList == null) {
      this.optList = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsArtilleryRecord>();
    }
    this.optList.add(elem);
  }

  /**
   * 占领记录
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<com.lc.billion.icefire.protocol.structure.PsArtilleryRecord> getOptList() {
    return this.optList;
  }

  /**
   * 占领记录
   */
  public GcRegionCapitalArtilleryHistory setOptList(@org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsArtilleryRecord> optList) {
    this.optList = optList;
    return this;
  }

  public void unsetOptList() {
    this.optList = null;
  }

  /** Returns true if field optList is set (has been assigned a value) and false otherwise */
  public boolean isSetOptList() {
    return this.optList != null;
  }

  public void setOptListIsSet(boolean value) {
    if (!value) {
      this.optList = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case OPT_LIST:
      if (value == null) {
        unsetOptList();
      } else {
        setOptList((java.util.List<com.lc.billion.icefire.protocol.structure.PsArtilleryRecord>)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case OPT_LIST:
      return getOptList();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case OPT_LIST:
      return isSetOptList();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcRegionCapitalArtilleryHistory)
      return this.equals((GcRegionCapitalArtilleryHistory)that);
    return false;
  }

  public boolean equals(GcRegionCapitalArtilleryHistory that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_optList = true && this.isSetOptList();
    boolean that_present_optList = true && that.isSetOptList();
    if (this_present_optList || that_present_optList) {
      if (!(this_present_optList && that_present_optList))
        return false;
      if (!this.optList.equals(that.optList))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetOptList()) ? 131071 : 524287);
    if (isSetOptList())
      hashCode = hashCode * 8191 + optList.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(GcRegionCapitalArtilleryHistory other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetOptList(), other.isSetOptList());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOptList()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.optList, other.optList);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcRegionCapitalArtilleryHistory(");
    boolean first = true;

    if (isSetOptList()) {
      sb.append("optList:");
      if (this.optList == null) {
        sb.append("null");
      } else {
        sb.append(this.optList);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcRegionCapitalArtilleryHistoryStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcRegionCapitalArtilleryHistoryStandardScheme getScheme() {
      return new GcRegionCapitalArtilleryHistoryStandardScheme();
    }
  }

  private static class GcRegionCapitalArtilleryHistoryStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcRegionCapitalArtilleryHistory> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcRegionCapitalArtilleryHistory struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 2: // OPT_LIST
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.optList = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsArtilleryRecord>(_list0.size);
                @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsArtilleryRecord _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new com.lc.billion.icefire.protocol.structure.PsArtilleryRecord();
                  _elem1.read(iprot);
                  struct.optList.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setOptListIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcRegionCapitalArtilleryHistory struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.optList != null) {
        if (struct.isSetOptList()) {
          oprot.writeFieldBegin(OPT_LIST_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.optList.size()));
            for (com.lc.billion.icefire.protocol.structure.PsArtilleryRecord _iter3 : struct.optList)
            {
              _iter3.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcRegionCapitalArtilleryHistoryTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcRegionCapitalArtilleryHistoryTupleScheme getScheme() {
      return new GcRegionCapitalArtilleryHistoryTupleScheme();
    }
  }

  private static class GcRegionCapitalArtilleryHistoryTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcRegionCapitalArtilleryHistory> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcRegionCapitalArtilleryHistory struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetOptList()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetOptList()) {
        {
          oprot.writeI32(struct.optList.size());
          for (com.lc.billion.icefire.protocol.structure.PsArtilleryRecord _iter4 : struct.optList)
          {
            _iter4.write(oprot);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcRegionCapitalArtilleryHistory struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TList _list5 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
          struct.optList = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsArtilleryRecord>(_list5.size);
          @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsArtilleryRecord _elem6;
          for (int _i7 = 0; _i7 < _list5.size; ++_i7)
          {
            _elem6 = new com.lc.billion.icefire.protocol.structure.PsArtilleryRecord();
            _elem6.read(iprot);
            struct.optList.add(_elem6);
          }
        }
        struct.setOptListIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

