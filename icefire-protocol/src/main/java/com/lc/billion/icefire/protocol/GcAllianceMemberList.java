/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 获取成员列表
 * @Message(816)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcAllianceMemberList implements org.apache.thrift.TBase<GcAllianceMemberList, GcAllianceMemberList._Fields>, java.io.Serializable, Cloneable, Comparable<GcAllianceMemberList> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcAllianceMemberList");

  private static final org.apache.thrift.protocol.TField MEMBERS_FIELD_DESC = new org.apache.thrift.protocol.TField("members", org.apache.thrift.protocol.TType.LIST, (short)1);
  private static final org.apache.thrift.protocol.TField ALLIANCE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("allianceId", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField POSITION_MAP_FIELD_DESC = new org.apache.thrift.protocol.TField("positionMap", org.apache.thrift.protocol.TType.MAP, (short)3);
  private static final org.apache.thrift.protocol.TField GOVERNOR_MAP_FIELD_DESC = new org.apache.thrift.protocol.TField("governorMap", org.apache.thrift.protocol.TType.MAP, (short)4);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcAllianceMemberListStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcAllianceMemberListTupleSchemeFactory();

  public @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsAllianceMemberInfo> members; // required
  public @org.apache.thrift.annotation.Nullable java.lang.String allianceId; // required
  /**
   * 官职对应的成员
   */
  public @org.apache.thrift.annotation.Nullable java.util.Map<java.lang.Integer,java.lang.Long> positionMap; // optional
  /**
   * 太守对应的成员 metaId - roleId
   */
  public @org.apache.thrift.annotation.Nullable java.util.Map<java.lang.String,java.lang.String> governorMap; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    MEMBERS((short)1, "members"),
    ALLIANCE_ID((short)2, "allianceId"),
    /**
     * 官职对应的成员
     */
    POSITION_MAP((short)3, "positionMap"),
    /**
     * 太守对应的成员 metaId - roleId
     */
    GOVERNOR_MAP((short)4, "governorMap");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // MEMBERS
          return MEMBERS;
        case 2: // ALLIANCE_ID
          return ALLIANCE_ID;
        case 3: // POSITION_MAP
          return POSITION_MAP;
        case 4: // GOVERNOR_MAP
          return GOVERNOR_MAP;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final _Fields optionals[] = {_Fields.POSITION_MAP,_Fields.GOVERNOR_MAP};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.MEMBERS, new org.apache.thrift.meta_data.FieldMetaData("members", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsAllianceMemberInfo.class))));
    tmpMap.put(_Fields.ALLIANCE_ID, new org.apache.thrift.meta_data.FieldMetaData("allianceId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.POSITION_MAP, new org.apache.thrift.meta_data.FieldMetaData("positionMap", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
    tmpMap.put(_Fields.GOVERNOR_MAP, new org.apache.thrift.meta_data.FieldMetaData("governorMap", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcAllianceMemberList.class, metaDataMap);
  }

  public GcAllianceMemberList() {
  }

  public GcAllianceMemberList(
    java.util.List<com.lc.billion.icefire.protocol.structure.PsAllianceMemberInfo> members,
    java.lang.String allianceId)
  {
    this();
    this.members = members;
    this.allianceId = allianceId;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcAllianceMemberList(GcAllianceMemberList other) {
    if (other.isSetMembers()) {
      java.util.List<com.lc.billion.icefire.protocol.structure.PsAllianceMemberInfo> __this__members = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsAllianceMemberInfo>(other.members.size());
      for (com.lc.billion.icefire.protocol.structure.PsAllianceMemberInfo other_element : other.members) {
        __this__members.add(new com.lc.billion.icefire.protocol.structure.PsAllianceMemberInfo(other_element));
      }
      this.members = __this__members;
    }
    if (other.isSetAllianceId()) {
      this.allianceId = other.allianceId;
    }
    if (other.isSetPositionMap()) {
      java.util.Map<java.lang.Integer,java.lang.Long> __this__positionMap = new java.util.HashMap<java.lang.Integer,java.lang.Long>(other.positionMap);
      this.positionMap = __this__positionMap;
    }
    if (other.isSetGovernorMap()) {
      java.util.Map<java.lang.String,java.lang.String> __this__governorMap = new java.util.HashMap<java.lang.String,java.lang.String>(other.governorMap);
      this.governorMap = __this__governorMap;
    }
  }

  public GcAllianceMemberList deepCopy() {
    return new GcAllianceMemberList(this);
  }

  @Override
  public void clear() {
    this.members = null;
    this.allianceId = null;
    this.positionMap = null;
    this.governorMap = null;
  }

  public int getMembersSize() {
    return (this.members == null) ? 0 : this.members.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<com.lc.billion.icefire.protocol.structure.PsAllianceMemberInfo> getMembersIterator() {
    return (this.members == null) ? null : this.members.iterator();
  }

  public void addToMembers(com.lc.billion.icefire.protocol.structure.PsAllianceMemberInfo elem) {
    if (this.members == null) {
      this.members = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsAllianceMemberInfo>();
    }
    this.members.add(elem);
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.List<com.lc.billion.icefire.protocol.structure.PsAllianceMemberInfo> getMembers() {
    return this.members;
  }

  public GcAllianceMemberList setMembers(@org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsAllianceMemberInfo> members) {
    this.members = members;
    return this;
  }

  public void unsetMembers() {
    this.members = null;
  }

  /** Returns true if field members is set (has been assigned a value) and false otherwise */
  public boolean isSetMembers() {
    return this.members != null;
  }

  public void setMembersIsSet(boolean value) {
    if (!value) {
      this.members = null;
    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.String getAllianceId() {
    return this.allianceId;
  }

  public GcAllianceMemberList setAllianceId(@org.apache.thrift.annotation.Nullable java.lang.String allianceId) {
    this.allianceId = allianceId;
    return this;
  }

  public void unsetAllianceId() {
    this.allianceId = null;
  }

  /** Returns true if field allianceId is set (has been assigned a value) and false otherwise */
  public boolean isSetAllianceId() {
    return this.allianceId != null;
  }

  public void setAllianceIdIsSet(boolean value) {
    if (!value) {
      this.allianceId = null;
    }
  }

  public int getPositionMapSize() {
    return (this.positionMap == null) ? 0 : this.positionMap.size();
  }

  public void putToPositionMap(int key, long val) {
    if (this.positionMap == null) {
      this.positionMap = new java.util.HashMap<java.lang.Integer,java.lang.Long>();
    }
    this.positionMap.put(key, val);
  }

  /**
   * 官职对应的成员
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.Map<java.lang.Integer,java.lang.Long> getPositionMap() {
    return this.positionMap;
  }

  /**
   * 官职对应的成员
   */
  public GcAllianceMemberList setPositionMap(@org.apache.thrift.annotation.Nullable java.util.Map<java.lang.Integer,java.lang.Long> positionMap) {
    this.positionMap = positionMap;
    return this;
  }

  public void unsetPositionMap() {
    this.positionMap = null;
  }

  /** Returns true if field positionMap is set (has been assigned a value) and false otherwise */
  public boolean isSetPositionMap() {
    return this.positionMap != null;
  }

  public void setPositionMapIsSet(boolean value) {
    if (!value) {
      this.positionMap = null;
    }
  }

  public int getGovernorMapSize() {
    return (this.governorMap == null) ? 0 : this.governorMap.size();
  }

  public void putToGovernorMap(java.lang.String key, java.lang.String val) {
    if (this.governorMap == null) {
      this.governorMap = new java.util.HashMap<java.lang.String,java.lang.String>();
    }
    this.governorMap.put(key, val);
  }

  /**
   * 太守对应的成员 metaId - roleId
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.Map<java.lang.String,java.lang.String> getGovernorMap() {
    return this.governorMap;
  }

  /**
   * 太守对应的成员 metaId - roleId
   */
  public GcAllianceMemberList setGovernorMap(@org.apache.thrift.annotation.Nullable java.util.Map<java.lang.String,java.lang.String> governorMap) {
    this.governorMap = governorMap;
    return this;
  }

  public void unsetGovernorMap() {
    this.governorMap = null;
  }

  /** Returns true if field governorMap is set (has been assigned a value) and false otherwise */
  public boolean isSetGovernorMap() {
    return this.governorMap != null;
  }

  public void setGovernorMapIsSet(boolean value) {
    if (!value) {
      this.governorMap = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case MEMBERS:
      if (value == null) {
        unsetMembers();
      } else {
        setMembers((java.util.List<com.lc.billion.icefire.protocol.structure.PsAllianceMemberInfo>)value);
      }
      break;

    case ALLIANCE_ID:
      if (value == null) {
        unsetAllianceId();
      } else {
        setAllianceId((java.lang.String)value);
      }
      break;

    case POSITION_MAP:
      if (value == null) {
        unsetPositionMap();
      } else {
        setPositionMap((java.util.Map<java.lang.Integer,java.lang.Long>)value);
      }
      break;

    case GOVERNOR_MAP:
      if (value == null) {
        unsetGovernorMap();
      } else {
        setGovernorMap((java.util.Map<java.lang.String,java.lang.String>)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case MEMBERS:
      return getMembers();

    case ALLIANCE_ID:
      return getAllianceId();

    case POSITION_MAP:
      return getPositionMap();

    case GOVERNOR_MAP:
      return getGovernorMap();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case MEMBERS:
      return isSetMembers();
    case ALLIANCE_ID:
      return isSetAllianceId();
    case POSITION_MAP:
      return isSetPositionMap();
    case GOVERNOR_MAP:
      return isSetGovernorMap();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcAllianceMemberList)
      return this.equals((GcAllianceMemberList)that);
    return false;
  }

  public boolean equals(GcAllianceMemberList that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_members = true && this.isSetMembers();
    boolean that_present_members = true && that.isSetMembers();
    if (this_present_members || that_present_members) {
      if (!(this_present_members && that_present_members))
        return false;
      if (!this.members.equals(that.members))
        return false;
    }

    boolean this_present_allianceId = true && this.isSetAllianceId();
    boolean that_present_allianceId = true && that.isSetAllianceId();
    if (this_present_allianceId || that_present_allianceId) {
      if (!(this_present_allianceId && that_present_allianceId))
        return false;
      if (!this.allianceId.equals(that.allianceId))
        return false;
    }

    boolean this_present_positionMap = true && this.isSetPositionMap();
    boolean that_present_positionMap = true && that.isSetPositionMap();
    if (this_present_positionMap || that_present_positionMap) {
      if (!(this_present_positionMap && that_present_positionMap))
        return false;
      if (!this.positionMap.equals(that.positionMap))
        return false;
    }

    boolean this_present_governorMap = true && this.isSetGovernorMap();
    boolean that_present_governorMap = true && that.isSetGovernorMap();
    if (this_present_governorMap || that_present_governorMap) {
      if (!(this_present_governorMap && that_present_governorMap))
        return false;
      if (!this.governorMap.equals(that.governorMap))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetMembers()) ? 131071 : 524287);
    if (isSetMembers())
      hashCode = hashCode * 8191 + members.hashCode();

    hashCode = hashCode * 8191 + ((isSetAllianceId()) ? 131071 : 524287);
    if (isSetAllianceId())
      hashCode = hashCode * 8191 + allianceId.hashCode();

    hashCode = hashCode * 8191 + ((isSetPositionMap()) ? 131071 : 524287);
    if (isSetPositionMap())
      hashCode = hashCode * 8191 + positionMap.hashCode();

    hashCode = hashCode * 8191 + ((isSetGovernorMap()) ? 131071 : 524287);
    if (isSetGovernorMap())
      hashCode = hashCode * 8191 + governorMap.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(GcAllianceMemberList other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetMembers(), other.isSetMembers());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMembers()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.members, other.members);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetAllianceId(), other.isSetAllianceId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAllianceId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.allianceId, other.allianceId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetPositionMap(), other.isSetPositionMap());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPositionMap()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.positionMap, other.positionMap);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetGovernorMap(), other.isSetGovernorMap());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGovernorMap()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.governorMap, other.governorMap);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcAllianceMemberList(");
    boolean first = true;

    sb.append("members:");
    if (this.members == null) {
      sb.append("null");
    } else {
      sb.append(this.members);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("allianceId:");
    if (this.allianceId == null) {
      sb.append("null");
    } else {
      sb.append(this.allianceId);
    }
    first = false;
    if (isSetPositionMap()) {
      if (!first) sb.append(", ");
      sb.append("positionMap:");
      if (this.positionMap == null) {
        sb.append("null");
      } else {
        sb.append(this.positionMap);
      }
      first = false;
    }
    if (isSetGovernorMap()) {
      if (!first) sb.append(", ");
      sb.append("governorMap:");
      if (this.governorMap == null) {
        sb.append("null");
      } else {
        sb.append(this.governorMap);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (members == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'members' was not present! Struct: " + toString());
    }
    if (allianceId == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'allianceId' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcAllianceMemberListStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcAllianceMemberListStandardScheme getScheme() {
      return new GcAllianceMemberListStandardScheme();
    }
  }

  private static class GcAllianceMemberListStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcAllianceMemberList> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcAllianceMemberList struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // MEMBERS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.members = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsAllianceMemberInfo>(_list0.size);
                @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsAllianceMemberInfo _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new com.lc.billion.icefire.protocol.structure.PsAllianceMemberInfo();
                  _elem1.read(iprot);
                  struct.members.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setMembersIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // ALLIANCE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.allianceId = iprot.readString();
              struct.setAllianceIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // POSITION_MAP
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map3 = iprot.readMapBegin();
                struct.positionMap = new java.util.HashMap<java.lang.Integer,java.lang.Long>(2*_map3.size);
                int _key4;
                long _val5;
                for (int _i6 = 0; _i6 < _map3.size; ++_i6)
                {
                  _key4 = iprot.readI32();
                  _val5 = iprot.readI64();
                  struct.positionMap.put(_key4, _val5);
                }
                iprot.readMapEnd();
              }
              struct.setPositionMapIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // GOVERNOR_MAP
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map7 = iprot.readMapBegin();
                struct.governorMap = new java.util.HashMap<java.lang.String,java.lang.String>(2*_map7.size);
                @org.apache.thrift.annotation.Nullable java.lang.String _key8;
                @org.apache.thrift.annotation.Nullable java.lang.String _val9;
                for (int _i10 = 0; _i10 < _map7.size; ++_i10)
                {
                  _key8 = iprot.readString();
                  _val9 = iprot.readString();
                  struct.governorMap.put(_key8, _val9);
                }
                iprot.readMapEnd();
              }
              struct.setGovernorMapIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcAllianceMemberList struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.members != null) {
        oprot.writeFieldBegin(MEMBERS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.members.size()));
          for (com.lc.billion.icefire.protocol.structure.PsAllianceMemberInfo _iter11 : struct.members)
          {
            _iter11.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.allianceId != null) {
        oprot.writeFieldBegin(ALLIANCE_ID_FIELD_DESC);
        oprot.writeString(struct.allianceId);
        oprot.writeFieldEnd();
      }
      if (struct.positionMap != null) {
        if (struct.isSetPositionMap()) {
          oprot.writeFieldBegin(POSITION_MAP_FIELD_DESC);
          {
            oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I32, org.apache.thrift.protocol.TType.I64, struct.positionMap.size()));
            for (java.util.Map.Entry<java.lang.Integer, java.lang.Long> _iter12 : struct.positionMap.entrySet())
            {
              oprot.writeI32(_iter12.getKey());
              oprot.writeI64(_iter12.getValue());
            }
            oprot.writeMapEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      if (struct.governorMap != null) {
        if (struct.isSetGovernorMap()) {
          oprot.writeFieldBegin(GOVERNOR_MAP_FIELD_DESC);
          {
            oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.governorMap.size()));
            for (java.util.Map.Entry<java.lang.String, java.lang.String> _iter13 : struct.governorMap.entrySet())
            {
              oprot.writeString(_iter13.getKey());
              oprot.writeString(_iter13.getValue());
            }
            oprot.writeMapEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcAllianceMemberListTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcAllianceMemberListTupleScheme getScheme() {
      return new GcAllianceMemberListTupleScheme();
    }
  }

  private static class GcAllianceMemberListTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcAllianceMemberList> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcAllianceMemberList struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      {
        oprot.writeI32(struct.members.size());
        for (com.lc.billion.icefire.protocol.structure.PsAllianceMemberInfo _iter14 : struct.members)
        {
          _iter14.write(oprot);
        }
      }
      oprot.writeString(struct.allianceId);
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetPositionMap()) {
        optionals.set(0);
      }
      if (struct.isSetGovernorMap()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetPositionMap()) {
        {
          oprot.writeI32(struct.positionMap.size());
          for (java.util.Map.Entry<java.lang.Integer, java.lang.Long> _iter15 : struct.positionMap.entrySet())
          {
            oprot.writeI32(_iter15.getKey());
            oprot.writeI64(_iter15.getValue());
          }
        }
      }
      if (struct.isSetGovernorMap()) {
        {
          oprot.writeI32(struct.governorMap.size());
          for (java.util.Map.Entry<java.lang.String, java.lang.String> _iter16 : struct.governorMap.entrySet())
          {
            oprot.writeString(_iter16.getKey());
            oprot.writeString(_iter16.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcAllianceMemberList struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      {
        org.apache.thrift.protocol.TList _list17 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
        struct.members = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsAllianceMemberInfo>(_list17.size);
        @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsAllianceMemberInfo _elem18;
        for (int _i19 = 0; _i19 < _list17.size; ++_i19)
        {
          _elem18 = new com.lc.billion.icefire.protocol.structure.PsAllianceMemberInfo();
          _elem18.read(iprot);
          struct.members.add(_elem18);
        }
      }
      struct.setMembersIsSet(true);
      struct.allianceId = iprot.readString();
      struct.setAllianceIdIsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TMap _map20 = iprot.readMapBegin(org.apache.thrift.protocol.TType.I32, org.apache.thrift.protocol.TType.I64); 
          struct.positionMap = new java.util.HashMap<java.lang.Integer,java.lang.Long>(2*_map20.size);
          int _key21;
          long _val22;
          for (int _i23 = 0; _i23 < _map20.size; ++_i23)
          {
            _key21 = iprot.readI32();
            _val22 = iprot.readI64();
            struct.positionMap.put(_key21, _val22);
          }
        }
        struct.setPositionMapIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TMap _map24 = iprot.readMapBegin(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING); 
          struct.governorMap = new java.util.HashMap<java.lang.String,java.lang.String>(2*_map24.size);
          @org.apache.thrift.annotation.Nullable java.lang.String _key25;
          @org.apache.thrift.annotation.Nullable java.lang.String _val26;
          for (int _i27 = 0; _i27 < _map24.size; ++_i27)
          {
            _key25 = iprot.readString();
            _val26 = iprot.readString();
            struct.governorMap.put(_key25, _val26);
          }
        }
        struct.setGovernorMapIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

