/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 头像 上传审核结果
 * @Message(1408)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcAvatarUploadAudit implements org.apache.thrift.TBase<GcAvatarUploadAudit, GcAvatarUploadAudit._Fields>, java.io.Serializable, Cloneable, Comparable<GcAvatarUploadAudit> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcAvatarUploadAudit");

  private static final org.apache.thrift.protocol.TField PASSED_FIELD_DESC = new org.apache.thrift.protocol.TField("passed", org.apache.thrift.protocol.TType.BOOL, (short)1);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcAvatarUploadAuditStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcAvatarUploadAuditTupleSchemeFactory();

  /**
   * 审核是否通过
   */
  public boolean passed; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 审核是否通过
     */
    PASSED((short)1, "passed");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // PASSED
          return PASSED;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __PASSED_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.PASSED};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.PASSED, new org.apache.thrift.meta_data.FieldMetaData("passed", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcAvatarUploadAudit.class, metaDataMap);
  }

  public GcAvatarUploadAudit() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcAvatarUploadAudit(GcAvatarUploadAudit other) {
    __isset_bitfield = other.__isset_bitfield;
    this.passed = other.passed;
  }

  public GcAvatarUploadAudit deepCopy() {
    return new GcAvatarUploadAudit(this);
  }

  @Override
  public void clear() {
    setPassedIsSet(false);
    this.passed = false;
  }

  /**
   * 审核是否通过
   */
  public boolean isPassed() {
    return this.passed;
  }

  /**
   * 审核是否通过
   */
  public GcAvatarUploadAudit setPassed(boolean passed) {
    this.passed = passed;
    setPassedIsSet(true);
    return this;
  }

  public void unsetPassed() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __PASSED_ISSET_ID);
  }

  /** Returns true if field passed is set (has been assigned a value) and false otherwise */
  public boolean isSetPassed() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __PASSED_ISSET_ID);
  }

  public void setPassedIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __PASSED_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case PASSED:
      if (value == null) {
        unsetPassed();
      } else {
        setPassed((java.lang.Boolean)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case PASSED:
      return isPassed();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case PASSED:
      return isSetPassed();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcAvatarUploadAudit)
      return this.equals((GcAvatarUploadAudit)that);
    return false;
  }

  public boolean equals(GcAvatarUploadAudit that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_passed = true && this.isSetPassed();
    boolean that_present_passed = true && that.isSetPassed();
    if (this_present_passed || that_present_passed) {
      if (!(this_present_passed && that_present_passed))
        return false;
      if (this.passed != that.passed)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetPassed()) ? 131071 : 524287);
    if (isSetPassed())
      hashCode = hashCode * 8191 + ((passed) ? 131071 : 524287);

    return hashCode;
  }

  @Override
  public int compareTo(GcAvatarUploadAudit other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetPassed(), other.isSetPassed());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPassed()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.passed, other.passed);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcAvatarUploadAudit(");
    boolean first = true;

    if (isSetPassed()) {
      sb.append("passed:");
      sb.append(this.passed);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcAvatarUploadAuditStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcAvatarUploadAuditStandardScheme getScheme() {
      return new GcAvatarUploadAuditStandardScheme();
    }
  }

  private static class GcAvatarUploadAuditStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcAvatarUploadAudit> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcAvatarUploadAudit struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // PASSED
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.passed = iprot.readBool();
              struct.setPassedIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcAvatarUploadAudit struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.isSetPassed()) {
        oprot.writeFieldBegin(PASSED_FIELD_DESC);
        oprot.writeBool(struct.passed);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcAvatarUploadAuditTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcAvatarUploadAuditTupleScheme getScheme() {
      return new GcAvatarUploadAuditTupleScheme();
    }
  }

  private static class GcAvatarUploadAuditTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcAvatarUploadAudit> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcAvatarUploadAudit struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetPassed()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetPassed()) {
        oprot.writeBool(struct.passed);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcAvatarUploadAudit struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        struct.passed = iprot.readBool();
        struct.setPassedIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

