/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.structure;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 商店信息
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class PsStoreInfo implements org.apache.thrift.TBase<PsStoreInfo, PsStoreInfo._Fields>, java.io.Serializable, Cloneable, Comparable<PsStoreInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PsStoreInfo");

  private static final org.apache.thrift.protocol.TField STORE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("storeId", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField COMMODITY_INFOS_FIELD_DESC = new org.apache.thrift.protocol.TField("commodityInfos", org.apache.thrift.protocol.TType.LIST, (short)2);
  private static final org.apache.thrift.protocol.TField EXPIRATION_TS_FIELD_DESC = new org.apache.thrift.protocol.TField("expirationTs", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField NEXT_REFRESH_TS_FIELD_DESC = new org.apache.thrift.protocol.TField("nextRefreshTs", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField MANUAL_COST_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("manualCostType", org.apache.thrift.protocol.TType.I32, (short)5);
  private static final org.apache.thrift.protocol.TField MANUAL_COST_FIELD_DESC = new org.apache.thrift.protocol.TField("manualCost", org.apache.thrift.protocol.TType.I64, (short)6);
  private static final org.apache.thrift.protocol.TField MANUAL_COUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("manualCount", org.apache.thrift.protocol.TType.I32, (short)7);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new PsStoreInfoStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new PsStoreInfoTupleSchemeFactory();

  /**
   * 商店类型
   */
  public int storeId; // required
  /**
   * 商品列表
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<PsStoreCommodityInfo> commodityInfos; // required
  /**
   * 过期时间
   */
  public long expirationTs; // required
  /**
   * 下一次刷新时间
   */
  public long nextRefreshTs; // optional
  /**
   * 手动刷新消耗货币类型
   */
  public int manualCostType; // optional
  /**
   * 手动刷新消耗货币数量
   */
  public long manualCost; // optional
  /**
   * 手动刷新次数
   */
  public int manualCount; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 商店类型
     */
    STORE_ID((short)1, "storeId"),
    /**
     * 商品列表
     */
    COMMODITY_INFOS((short)2, "commodityInfos"),
    /**
     * 过期时间
     */
    EXPIRATION_TS((short)3, "expirationTs"),
    /**
     * 下一次刷新时间
     */
    NEXT_REFRESH_TS((short)4, "nextRefreshTs"),
    /**
     * 手动刷新消耗货币类型
     */
    MANUAL_COST_TYPE((short)5, "manualCostType"),
    /**
     * 手动刷新消耗货币数量
     */
    MANUAL_COST((short)6, "manualCost"),
    /**
     * 手动刷新次数
     */
    MANUAL_COUNT((short)7, "manualCount");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // STORE_ID
          return STORE_ID;
        case 2: // COMMODITY_INFOS
          return COMMODITY_INFOS;
        case 3: // EXPIRATION_TS
          return EXPIRATION_TS;
        case 4: // NEXT_REFRESH_TS
          return NEXT_REFRESH_TS;
        case 5: // MANUAL_COST_TYPE
          return MANUAL_COST_TYPE;
        case 6: // MANUAL_COST
          return MANUAL_COST;
        case 7: // MANUAL_COUNT
          return MANUAL_COUNT;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __STOREID_ISSET_ID = 0;
  private static final int __EXPIRATIONTS_ISSET_ID = 1;
  private static final int __NEXTREFRESHTS_ISSET_ID = 2;
  private static final int __MANUALCOSTTYPE_ISSET_ID = 3;
  private static final int __MANUALCOST_ISSET_ID = 4;
  private static final int __MANUALCOUNT_ISSET_ID = 5;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.NEXT_REFRESH_TS,_Fields.MANUAL_COST_TYPE,_Fields.MANUAL_COST,_Fields.MANUAL_COUNT};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.STORE_ID, new org.apache.thrift.meta_data.FieldMetaData("storeId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.COMMODITY_INFOS, new org.apache.thrift.meta_data.FieldMetaData("commodityInfos", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, PsStoreCommodityInfo.class))));
    tmpMap.put(_Fields.EXPIRATION_TS, new org.apache.thrift.meta_data.FieldMetaData("expirationTs", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.NEXT_REFRESH_TS, new org.apache.thrift.meta_data.FieldMetaData("nextRefreshTs", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.MANUAL_COST_TYPE, new org.apache.thrift.meta_data.FieldMetaData("manualCostType", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.MANUAL_COST, new org.apache.thrift.meta_data.FieldMetaData("manualCost", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.MANUAL_COUNT, new org.apache.thrift.meta_data.FieldMetaData("manualCount", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PsStoreInfo.class, metaDataMap);
  }

  public PsStoreInfo() {
  }

  public PsStoreInfo(
    int storeId,
    java.util.List<PsStoreCommodityInfo> commodityInfos,
    long expirationTs)
  {
    this();
    this.storeId = storeId;
    setStoreIdIsSet(true);
    this.commodityInfos = commodityInfos;
    this.expirationTs = expirationTs;
    setExpirationTsIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PsStoreInfo(PsStoreInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    this.storeId = other.storeId;
    if (other.isSetCommodityInfos()) {
      java.util.List<PsStoreCommodityInfo> __this__commodityInfos = new java.util.ArrayList<PsStoreCommodityInfo>(other.commodityInfos.size());
      for (PsStoreCommodityInfo other_element : other.commodityInfos) {
        __this__commodityInfos.add(new PsStoreCommodityInfo(other_element));
      }
      this.commodityInfos = __this__commodityInfos;
    }
    this.expirationTs = other.expirationTs;
    this.nextRefreshTs = other.nextRefreshTs;
    this.manualCostType = other.manualCostType;
    this.manualCost = other.manualCost;
    this.manualCount = other.manualCount;
  }

  public PsStoreInfo deepCopy() {
    return new PsStoreInfo(this);
  }

  @Override
  public void clear() {
    setStoreIdIsSet(false);
    this.storeId = 0;
    this.commodityInfos = null;
    setExpirationTsIsSet(false);
    this.expirationTs = 0;
    setNextRefreshTsIsSet(false);
    this.nextRefreshTs = 0;
    setManualCostTypeIsSet(false);
    this.manualCostType = 0;
    setManualCostIsSet(false);
    this.manualCost = 0;
    setManualCountIsSet(false);
    this.manualCount = 0;
  }

  /**
   * 商店类型
   */
  public int getStoreId() {
    return this.storeId;
  }

  /**
   * 商店类型
   */
  public PsStoreInfo setStoreId(int storeId) {
    this.storeId = storeId;
    setStoreIdIsSet(true);
    return this;
  }

  public void unsetStoreId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __STOREID_ISSET_ID);
  }

  /** Returns true if field storeId is set (has been assigned a value) and false otherwise */
  public boolean isSetStoreId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __STOREID_ISSET_ID);
  }

  public void setStoreIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __STOREID_ISSET_ID, value);
  }

  public int getCommodityInfosSize() {
    return (this.commodityInfos == null) ? 0 : this.commodityInfos.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<PsStoreCommodityInfo> getCommodityInfosIterator() {
    return (this.commodityInfos == null) ? null : this.commodityInfos.iterator();
  }

  public void addToCommodityInfos(PsStoreCommodityInfo elem) {
    if (this.commodityInfos == null) {
      this.commodityInfos = new java.util.ArrayList<PsStoreCommodityInfo>();
    }
    this.commodityInfos.add(elem);
  }

  /**
   * 商品列表
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<PsStoreCommodityInfo> getCommodityInfos() {
    return this.commodityInfos;
  }

  /**
   * 商品列表
   */
  public PsStoreInfo setCommodityInfos(@org.apache.thrift.annotation.Nullable java.util.List<PsStoreCommodityInfo> commodityInfos) {
    this.commodityInfos = commodityInfos;
    return this;
  }

  public void unsetCommodityInfos() {
    this.commodityInfos = null;
  }

  /** Returns true if field commodityInfos is set (has been assigned a value) and false otherwise */
  public boolean isSetCommodityInfos() {
    return this.commodityInfos != null;
  }

  public void setCommodityInfosIsSet(boolean value) {
    if (!value) {
      this.commodityInfos = null;
    }
  }

  /**
   * 过期时间
   */
  public long getExpirationTs() {
    return this.expirationTs;
  }

  /**
   * 过期时间
   */
  public PsStoreInfo setExpirationTs(long expirationTs) {
    this.expirationTs = expirationTs;
    setExpirationTsIsSet(true);
    return this;
  }

  public void unsetExpirationTs() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __EXPIRATIONTS_ISSET_ID);
  }

  /** Returns true if field expirationTs is set (has been assigned a value) and false otherwise */
  public boolean isSetExpirationTs() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __EXPIRATIONTS_ISSET_ID);
  }

  public void setExpirationTsIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __EXPIRATIONTS_ISSET_ID, value);
  }

  /**
   * 下一次刷新时间
   */
  public long getNextRefreshTs() {
    return this.nextRefreshTs;
  }

  /**
   * 下一次刷新时间
   */
  public PsStoreInfo setNextRefreshTs(long nextRefreshTs) {
    this.nextRefreshTs = nextRefreshTs;
    setNextRefreshTsIsSet(true);
    return this;
  }

  public void unsetNextRefreshTs() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __NEXTREFRESHTS_ISSET_ID);
  }

  /** Returns true if field nextRefreshTs is set (has been assigned a value) and false otherwise */
  public boolean isSetNextRefreshTs() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __NEXTREFRESHTS_ISSET_ID);
  }

  public void setNextRefreshTsIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __NEXTREFRESHTS_ISSET_ID, value);
  }

  /**
   * 手动刷新消耗货币类型
   */
  public int getManualCostType() {
    return this.manualCostType;
  }

  /**
   * 手动刷新消耗货币类型
   */
  public PsStoreInfo setManualCostType(int manualCostType) {
    this.manualCostType = manualCostType;
    setManualCostTypeIsSet(true);
    return this;
  }

  public void unsetManualCostType() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __MANUALCOSTTYPE_ISSET_ID);
  }

  /** Returns true if field manualCostType is set (has been assigned a value) and false otherwise */
  public boolean isSetManualCostType() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __MANUALCOSTTYPE_ISSET_ID);
  }

  public void setManualCostTypeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __MANUALCOSTTYPE_ISSET_ID, value);
  }

  /**
   * 手动刷新消耗货币数量
   */
  public long getManualCost() {
    return this.manualCost;
  }

  /**
   * 手动刷新消耗货币数量
   */
  public PsStoreInfo setManualCost(long manualCost) {
    this.manualCost = manualCost;
    setManualCostIsSet(true);
    return this;
  }

  public void unsetManualCost() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __MANUALCOST_ISSET_ID);
  }

  /** Returns true if field manualCost is set (has been assigned a value) and false otherwise */
  public boolean isSetManualCost() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __MANUALCOST_ISSET_ID);
  }

  public void setManualCostIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __MANUALCOST_ISSET_ID, value);
  }

  /**
   * 手动刷新次数
   */
  public int getManualCount() {
    return this.manualCount;
  }

  /**
   * 手动刷新次数
   */
  public PsStoreInfo setManualCount(int manualCount) {
    this.manualCount = manualCount;
    setManualCountIsSet(true);
    return this;
  }

  public void unsetManualCount() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __MANUALCOUNT_ISSET_ID);
  }

  /** Returns true if field manualCount is set (has been assigned a value) and false otherwise */
  public boolean isSetManualCount() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __MANUALCOUNT_ISSET_ID);
  }

  public void setManualCountIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __MANUALCOUNT_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case STORE_ID:
      if (value == null) {
        unsetStoreId();
      } else {
        setStoreId((java.lang.Integer)value);
      }
      break;

    case COMMODITY_INFOS:
      if (value == null) {
        unsetCommodityInfos();
      } else {
        setCommodityInfos((java.util.List<PsStoreCommodityInfo>)value);
      }
      break;

    case EXPIRATION_TS:
      if (value == null) {
        unsetExpirationTs();
      } else {
        setExpirationTs((java.lang.Long)value);
      }
      break;

    case NEXT_REFRESH_TS:
      if (value == null) {
        unsetNextRefreshTs();
      } else {
        setNextRefreshTs((java.lang.Long)value);
      }
      break;

    case MANUAL_COST_TYPE:
      if (value == null) {
        unsetManualCostType();
      } else {
        setManualCostType((java.lang.Integer)value);
      }
      break;

    case MANUAL_COST:
      if (value == null) {
        unsetManualCost();
      } else {
        setManualCost((java.lang.Long)value);
      }
      break;

    case MANUAL_COUNT:
      if (value == null) {
        unsetManualCount();
      } else {
        setManualCount((java.lang.Integer)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case STORE_ID:
      return getStoreId();

    case COMMODITY_INFOS:
      return getCommodityInfos();

    case EXPIRATION_TS:
      return getExpirationTs();

    case NEXT_REFRESH_TS:
      return getNextRefreshTs();

    case MANUAL_COST_TYPE:
      return getManualCostType();

    case MANUAL_COST:
      return getManualCost();

    case MANUAL_COUNT:
      return getManualCount();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case STORE_ID:
      return isSetStoreId();
    case COMMODITY_INFOS:
      return isSetCommodityInfos();
    case EXPIRATION_TS:
      return isSetExpirationTs();
    case NEXT_REFRESH_TS:
      return isSetNextRefreshTs();
    case MANUAL_COST_TYPE:
      return isSetManualCostType();
    case MANUAL_COST:
      return isSetManualCost();
    case MANUAL_COUNT:
      return isSetManualCount();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof PsStoreInfo)
      return this.equals((PsStoreInfo)that);
    return false;
  }

  public boolean equals(PsStoreInfo that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_storeId = true;
    boolean that_present_storeId = true;
    if (this_present_storeId || that_present_storeId) {
      if (!(this_present_storeId && that_present_storeId))
        return false;
      if (this.storeId != that.storeId)
        return false;
    }

    boolean this_present_commodityInfos = true && this.isSetCommodityInfos();
    boolean that_present_commodityInfos = true && that.isSetCommodityInfos();
    if (this_present_commodityInfos || that_present_commodityInfos) {
      if (!(this_present_commodityInfos && that_present_commodityInfos))
        return false;
      if (!this.commodityInfos.equals(that.commodityInfos))
        return false;
    }

    boolean this_present_expirationTs = true;
    boolean that_present_expirationTs = true;
    if (this_present_expirationTs || that_present_expirationTs) {
      if (!(this_present_expirationTs && that_present_expirationTs))
        return false;
      if (this.expirationTs != that.expirationTs)
        return false;
    }

    boolean this_present_nextRefreshTs = true && this.isSetNextRefreshTs();
    boolean that_present_nextRefreshTs = true && that.isSetNextRefreshTs();
    if (this_present_nextRefreshTs || that_present_nextRefreshTs) {
      if (!(this_present_nextRefreshTs && that_present_nextRefreshTs))
        return false;
      if (this.nextRefreshTs != that.nextRefreshTs)
        return false;
    }

    boolean this_present_manualCostType = true && this.isSetManualCostType();
    boolean that_present_manualCostType = true && that.isSetManualCostType();
    if (this_present_manualCostType || that_present_manualCostType) {
      if (!(this_present_manualCostType && that_present_manualCostType))
        return false;
      if (this.manualCostType != that.manualCostType)
        return false;
    }

    boolean this_present_manualCost = true && this.isSetManualCost();
    boolean that_present_manualCost = true && that.isSetManualCost();
    if (this_present_manualCost || that_present_manualCost) {
      if (!(this_present_manualCost && that_present_manualCost))
        return false;
      if (this.manualCost != that.manualCost)
        return false;
    }

    boolean this_present_manualCount = true && this.isSetManualCount();
    boolean that_present_manualCount = true && that.isSetManualCount();
    if (this_present_manualCount || that_present_manualCount) {
      if (!(this_present_manualCount && that_present_manualCount))
        return false;
      if (this.manualCount != that.manualCount)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + storeId;

    hashCode = hashCode * 8191 + ((isSetCommodityInfos()) ? 131071 : 524287);
    if (isSetCommodityInfos())
      hashCode = hashCode * 8191 + commodityInfos.hashCode();

    hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(expirationTs);

    hashCode = hashCode * 8191 + ((isSetNextRefreshTs()) ? 131071 : 524287);
    if (isSetNextRefreshTs())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(nextRefreshTs);

    hashCode = hashCode * 8191 + ((isSetManualCostType()) ? 131071 : 524287);
    if (isSetManualCostType())
      hashCode = hashCode * 8191 + manualCostType;

    hashCode = hashCode * 8191 + ((isSetManualCost()) ? 131071 : 524287);
    if (isSetManualCost())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(manualCost);

    hashCode = hashCode * 8191 + ((isSetManualCount()) ? 131071 : 524287);
    if (isSetManualCount())
      hashCode = hashCode * 8191 + manualCount;

    return hashCode;
  }

  @Override
  public int compareTo(PsStoreInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetStoreId(), other.isSetStoreId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStoreId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.storeId, other.storeId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetCommodityInfos(), other.isSetCommodityInfos());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCommodityInfos()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.commodityInfos, other.commodityInfos);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetExpirationTs(), other.isSetExpirationTs());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExpirationTs()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.expirationTs, other.expirationTs);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetNextRefreshTs(), other.isSetNextRefreshTs());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetNextRefreshTs()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.nextRefreshTs, other.nextRefreshTs);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetManualCostType(), other.isSetManualCostType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetManualCostType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.manualCostType, other.manualCostType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetManualCost(), other.isSetManualCost());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetManualCost()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.manualCost, other.manualCost);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetManualCount(), other.isSetManualCount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetManualCount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.manualCount, other.manualCount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("PsStoreInfo(");
    boolean first = true;

    sb.append("storeId:");
    sb.append(this.storeId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("commodityInfos:");
    if (this.commodityInfos == null) {
      sb.append("null");
    } else {
      sb.append(this.commodityInfos);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("expirationTs:");
    sb.append(this.expirationTs);
    first = false;
    if (isSetNextRefreshTs()) {
      if (!first) sb.append(", ");
      sb.append("nextRefreshTs:");
      sb.append(this.nextRefreshTs);
      first = false;
    }
    if (isSetManualCostType()) {
      if (!first) sb.append(", ");
      sb.append("manualCostType:");
      sb.append(this.manualCostType);
      first = false;
    }
    if (isSetManualCost()) {
      if (!first) sb.append(", ");
      sb.append("manualCost:");
      sb.append(this.manualCost);
      first = false;
    }
    if (isSetManualCount()) {
      if (!first) sb.append(", ");
      sb.append("manualCount:");
      sb.append(this.manualCount);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'storeId' because it's a primitive and you chose the non-beans generator.
    if (commodityInfos == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'commodityInfos' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'expirationTs' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PsStoreInfoStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsStoreInfoStandardScheme getScheme() {
      return new PsStoreInfoStandardScheme();
    }
  }

  private static class PsStoreInfoStandardScheme extends org.apache.thrift.scheme.StandardScheme<PsStoreInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PsStoreInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // STORE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.storeId = iprot.readI32();
              struct.setStoreIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // COMMODITY_INFOS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.commodityInfos = new java.util.ArrayList<PsStoreCommodityInfo>(_list0.size);
                @org.apache.thrift.annotation.Nullable PsStoreCommodityInfo _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new PsStoreCommodityInfo();
                  _elem1.read(iprot);
                  struct.commodityInfos.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setCommodityInfosIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // EXPIRATION_TS
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.expirationTs = iprot.readI64();
              struct.setExpirationTsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // NEXT_REFRESH_TS
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.nextRefreshTs = iprot.readI64();
              struct.setNextRefreshTsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // MANUAL_COST_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.manualCostType = iprot.readI32();
              struct.setManualCostTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // MANUAL_COST
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.manualCost = iprot.readI64();
              struct.setManualCostIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // MANUAL_COUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.manualCount = iprot.readI32();
              struct.setManualCountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetStoreId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'storeId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetExpirationTs()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'expirationTs' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PsStoreInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(STORE_ID_FIELD_DESC);
      oprot.writeI32(struct.storeId);
      oprot.writeFieldEnd();
      if (struct.commodityInfos != null) {
        oprot.writeFieldBegin(COMMODITY_INFOS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.commodityInfos.size()));
          for (PsStoreCommodityInfo _iter3 : struct.commodityInfos)
          {
            _iter3.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(EXPIRATION_TS_FIELD_DESC);
      oprot.writeI64(struct.expirationTs);
      oprot.writeFieldEnd();
      if (struct.isSetNextRefreshTs()) {
        oprot.writeFieldBegin(NEXT_REFRESH_TS_FIELD_DESC);
        oprot.writeI64(struct.nextRefreshTs);
        oprot.writeFieldEnd();
      }
      if (struct.isSetManualCostType()) {
        oprot.writeFieldBegin(MANUAL_COST_TYPE_FIELD_DESC);
        oprot.writeI32(struct.manualCostType);
        oprot.writeFieldEnd();
      }
      if (struct.isSetManualCost()) {
        oprot.writeFieldBegin(MANUAL_COST_FIELD_DESC);
        oprot.writeI64(struct.manualCost);
        oprot.writeFieldEnd();
      }
      if (struct.isSetManualCount()) {
        oprot.writeFieldBegin(MANUAL_COUNT_FIELD_DESC);
        oprot.writeI32(struct.manualCount);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PsStoreInfoTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsStoreInfoTupleScheme getScheme() {
      return new PsStoreInfoTupleScheme();
    }
  }

  private static class PsStoreInfoTupleScheme extends org.apache.thrift.scheme.TupleScheme<PsStoreInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PsStoreInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI32(struct.storeId);
      {
        oprot.writeI32(struct.commodityInfos.size());
        for (PsStoreCommodityInfo _iter4 : struct.commodityInfos)
        {
          _iter4.write(oprot);
        }
      }
      oprot.writeI64(struct.expirationTs);
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetNextRefreshTs()) {
        optionals.set(0);
      }
      if (struct.isSetManualCostType()) {
        optionals.set(1);
      }
      if (struct.isSetManualCost()) {
        optionals.set(2);
      }
      if (struct.isSetManualCount()) {
        optionals.set(3);
      }
      oprot.writeBitSet(optionals, 4);
      if (struct.isSetNextRefreshTs()) {
        oprot.writeI64(struct.nextRefreshTs);
      }
      if (struct.isSetManualCostType()) {
        oprot.writeI32(struct.manualCostType);
      }
      if (struct.isSetManualCost()) {
        oprot.writeI64(struct.manualCost);
      }
      if (struct.isSetManualCount()) {
        oprot.writeI32(struct.manualCount);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PsStoreInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.storeId = iprot.readI32();
      struct.setStoreIdIsSet(true);
      {
        org.apache.thrift.protocol.TList _list5 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
        struct.commodityInfos = new java.util.ArrayList<PsStoreCommodityInfo>(_list5.size);
        @org.apache.thrift.annotation.Nullable PsStoreCommodityInfo _elem6;
        for (int _i7 = 0; _i7 < _list5.size; ++_i7)
        {
          _elem6 = new PsStoreCommodityInfo();
          _elem6.read(iprot);
          struct.commodityInfos.add(_elem6);
        }
      }
      struct.setCommodityInfosIsSet(true);
      struct.expirationTs = iprot.readI64();
      struct.setExpirationTsIsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(4);
      if (incoming.get(0)) {
        struct.nextRefreshTs = iprot.readI64();
        struct.setNextRefreshTsIsSet(true);
      }
      if (incoming.get(1)) {
        struct.manualCostType = iprot.readI32();
        struct.setManualCostTypeIsSet(true);
      }
      if (incoming.get(2)) {
        struct.manualCost = iprot.readI64();
        struct.setManualCostIsSet(true);
      }
      if (incoming.get(3)) {
        struct.manualCount = iprot.readI32();
        struct.setManualCountIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

