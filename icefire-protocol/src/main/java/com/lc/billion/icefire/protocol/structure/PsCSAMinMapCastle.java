/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.structure;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 跨服夺城小地图城市结构*
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class PsCSAMinMapCastle implements org.apache.thrift.TBase<PsCSAMinMapCastle, PsCSAMinMapCastle._Fields>, java.io.Serializable, Cloneable, Comparable<PsCSAMinMapCastle> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PsCSAMinMapCastle");

  private static final org.apache.thrift.protocol.TField GARRISON_SERVER_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("garrisonServerId", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField META_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("metaId", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField CSA_ATTACK_OCCUPY_FIELD_DESC = new org.apache.thrift.protocol.TField("csaAttackOccupy", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField SETOUT_COUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("setoutCount", org.apache.thrift.protocol.TType.I32, (short)4);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new PsCSAMinMapCastleStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new PsCSAMinMapCastleTupleSchemeFactory();

  public int garrisonServerId; // optional
  public @org.apache.thrift.annotation.Nullable java.lang.String metaId; // optional
  public long csaAttackOccupy; // optional
  /**
   * 发起行军数量
   */
  public int setoutCount; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    GARRISON_SERVER_ID((short)1, "garrisonServerId"),
    META_ID((short)2, "metaId"),
    CSA_ATTACK_OCCUPY((short)3, "csaAttackOccupy"),
    /**
     * 发起行军数量
     */
    SETOUT_COUNT((short)4, "setoutCount");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // GARRISON_SERVER_ID
          return GARRISON_SERVER_ID;
        case 2: // META_ID
          return META_ID;
        case 3: // CSA_ATTACK_OCCUPY
          return CSA_ATTACK_OCCUPY;
        case 4: // SETOUT_COUNT
          return SETOUT_COUNT;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __GARRISONSERVERID_ISSET_ID = 0;
  private static final int __CSAATTACKOCCUPY_ISSET_ID = 1;
  private static final int __SETOUTCOUNT_ISSET_ID = 2;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.GARRISON_SERVER_ID,_Fields.META_ID,_Fields.CSA_ATTACK_OCCUPY,_Fields.SETOUT_COUNT};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.GARRISON_SERVER_ID, new org.apache.thrift.meta_data.FieldMetaData("garrisonServerId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.META_ID, new org.apache.thrift.meta_data.FieldMetaData("metaId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.CSA_ATTACK_OCCUPY, new org.apache.thrift.meta_data.FieldMetaData("csaAttackOccupy", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SETOUT_COUNT, new org.apache.thrift.meta_data.FieldMetaData("setoutCount", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PsCSAMinMapCastle.class, metaDataMap);
  }

  public PsCSAMinMapCastle() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PsCSAMinMapCastle(PsCSAMinMapCastle other) {
    __isset_bitfield = other.__isset_bitfield;
    this.garrisonServerId = other.garrisonServerId;
    if (other.isSetMetaId()) {
      this.metaId = other.metaId;
    }
    this.csaAttackOccupy = other.csaAttackOccupy;
    this.setoutCount = other.setoutCount;
  }

  public PsCSAMinMapCastle deepCopy() {
    return new PsCSAMinMapCastle(this);
  }

  @Override
  public void clear() {
    setGarrisonServerIdIsSet(false);
    this.garrisonServerId = 0;
    this.metaId = null;
    setCsaAttackOccupyIsSet(false);
    this.csaAttackOccupy = 0;
    setSetoutCountIsSet(false);
    this.setoutCount = 0;
  }

  public int getGarrisonServerId() {
    return this.garrisonServerId;
  }

  public PsCSAMinMapCastle setGarrisonServerId(int garrisonServerId) {
    this.garrisonServerId = garrisonServerId;
    setGarrisonServerIdIsSet(true);
    return this;
  }

  public void unsetGarrisonServerId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __GARRISONSERVERID_ISSET_ID);
  }

  /** Returns true if field garrisonServerId is set (has been assigned a value) and false otherwise */
  public boolean isSetGarrisonServerId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __GARRISONSERVERID_ISSET_ID);
  }

  public void setGarrisonServerIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __GARRISONSERVERID_ISSET_ID, value);
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.String getMetaId() {
    return this.metaId;
  }

  public PsCSAMinMapCastle setMetaId(@org.apache.thrift.annotation.Nullable java.lang.String metaId) {
    this.metaId = metaId;
    return this;
  }

  public void unsetMetaId() {
    this.metaId = null;
  }

  /** Returns true if field metaId is set (has been assigned a value) and false otherwise */
  public boolean isSetMetaId() {
    return this.metaId != null;
  }

  public void setMetaIdIsSet(boolean value) {
    if (!value) {
      this.metaId = null;
    }
  }

  public long getCsaAttackOccupy() {
    return this.csaAttackOccupy;
  }

  public PsCSAMinMapCastle setCsaAttackOccupy(long csaAttackOccupy) {
    this.csaAttackOccupy = csaAttackOccupy;
    setCsaAttackOccupyIsSet(true);
    return this;
  }

  public void unsetCsaAttackOccupy() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __CSAATTACKOCCUPY_ISSET_ID);
  }

  /** Returns true if field csaAttackOccupy is set (has been assigned a value) and false otherwise */
  public boolean isSetCsaAttackOccupy() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __CSAATTACKOCCUPY_ISSET_ID);
  }

  public void setCsaAttackOccupyIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __CSAATTACKOCCUPY_ISSET_ID, value);
  }

  /**
   * 发起行军数量
   */
  public int getSetoutCount() {
    return this.setoutCount;
  }

  /**
   * 发起行军数量
   */
  public PsCSAMinMapCastle setSetoutCount(int setoutCount) {
    this.setoutCount = setoutCount;
    setSetoutCountIsSet(true);
    return this;
  }

  public void unsetSetoutCount() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __SETOUTCOUNT_ISSET_ID);
  }

  /** Returns true if field setoutCount is set (has been assigned a value) and false otherwise */
  public boolean isSetSetoutCount() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __SETOUTCOUNT_ISSET_ID);
  }

  public void setSetoutCountIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __SETOUTCOUNT_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case GARRISON_SERVER_ID:
      if (value == null) {
        unsetGarrisonServerId();
      } else {
        setGarrisonServerId((java.lang.Integer)value);
      }
      break;

    case META_ID:
      if (value == null) {
        unsetMetaId();
      } else {
        setMetaId((java.lang.String)value);
      }
      break;

    case CSA_ATTACK_OCCUPY:
      if (value == null) {
        unsetCsaAttackOccupy();
      } else {
        setCsaAttackOccupy((java.lang.Long)value);
      }
      break;

    case SETOUT_COUNT:
      if (value == null) {
        unsetSetoutCount();
      } else {
        setSetoutCount((java.lang.Integer)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case GARRISON_SERVER_ID:
      return getGarrisonServerId();

    case META_ID:
      return getMetaId();

    case CSA_ATTACK_OCCUPY:
      return getCsaAttackOccupy();

    case SETOUT_COUNT:
      return getSetoutCount();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case GARRISON_SERVER_ID:
      return isSetGarrisonServerId();
    case META_ID:
      return isSetMetaId();
    case CSA_ATTACK_OCCUPY:
      return isSetCsaAttackOccupy();
    case SETOUT_COUNT:
      return isSetSetoutCount();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof PsCSAMinMapCastle)
      return this.equals((PsCSAMinMapCastle)that);
    return false;
  }

  public boolean equals(PsCSAMinMapCastle that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_garrisonServerId = true && this.isSetGarrisonServerId();
    boolean that_present_garrisonServerId = true && that.isSetGarrisonServerId();
    if (this_present_garrisonServerId || that_present_garrisonServerId) {
      if (!(this_present_garrisonServerId && that_present_garrisonServerId))
        return false;
      if (this.garrisonServerId != that.garrisonServerId)
        return false;
    }

    boolean this_present_metaId = true && this.isSetMetaId();
    boolean that_present_metaId = true && that.isSetMetaId();
    if (this_present_metaId || that_present_metaId) {
      if (!(this_present_metaId && that_present_metaId))
        return false;
      if (!this.metaId.equals(that.metaId))
        return false;
    }

    boolean this_present_csaAttackOccupy = true && this.isSetCsaAttackOccupy();
    boolean that_present_csaAttackOccupy = true && that.isSetCsaAttackOccupy();
    if (this_present_csaAttackOccupy || that_present_csaAttackOccupy) {
      if (!(this_present_csaAttackOccupy && that_present_csaAttackOccupy))
        return false;
      if (this.csaAttackOccupy != that.csaAttackOccupy)
        return false;
    }

    boolean this_present_setoutCount = true && this.isSetSetoutCount();
    boolean that_present_setoutCount = true && that.isSetSetoutCount();
    if (this_present_setoutCount || that_present_setoutCount) {
      if (!(this_present_setoutCount && that_present_setoutCount))
        return false;
      if (this.setoutCount != that.setoutCount)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetGarrisonServerId()) ? 131071 : 524287);
    if (isSetGarrisonServerId())
      hashCode = hashCode * 8191 + garrisonServerId;

    hashCode = hashCode * 8191 + ((isSetMetaId()) ? 131071 : 524287);
    if (isSetMetaId())
      hashCode = hashCode * 8191 + metaId.hashCode();

    hashCode = hashCode * 8191 + ((isSetCsaAttackOccupy()) ? 131071 : 524287);
    if (isSetCsaAttackOccupy())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(csaAttackOccupy);

    hashCode = hashCode * 8191 + ((isSetSetoutCount()) ? 131071 : 524287);
    if (isSetSetoutCount())
      hashCode = hashCode * 8191 + setoutCount;

    return hashCode;
  }

  @Override
  public int compareTo(PsCSAMinMapCastle other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetGarrisonServerId(), other.isSetGarrisonServerId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGarrisonServerId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.garrisonServerId, other.garrisonServerId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetMetaId(), other.isSetMetaId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMetaId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.metaId, other.metaId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetCsaAttackOccupy(), other.isSetCsaAttackOccupy());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCsaAttackOccupy()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.csaAttackOccupy, other.csaAttackOccupy);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetSetoutCount(), other.isSetSetoutCount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSetoutCount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.setoutCount, other.setoutCount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("PsCSAMinMapCastle(");
    boolean first = true;

    if (isSetGarrisonServerId()) {
      sb.append("garrisonServerId:");
      sb.append(this.garrisonServerId);
      first = false;
    }
    if (isSetMetaId()) {
      if (!first) sb.append(", ");
      sb.append("metaId:");
      if (this.metaId == null) {
        sb.append("null");
      } else {
        sb.append(this.metaId);
      }
      first = false;
    }
    if (isSetCsaAttackOccupy()) {
      if (!first) sb.append(", ");
      sb.append("csaAttackOccupy:");
      sb.append(this.csaAttackOccupy);
      first = false;
    }
    if (isSetSetoutCount()) {
      if (!first) sb.append(", ");
      sb.append("setoutCount:");
      sb.append(this.setoutCount);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PsCSAMinMapCastleStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsCSAMinMapCastleStandardScheme getScheme() {
      return new PsCSAMinMapCastleStandardScheme();
    }
  }

  private static class PsCSAMinMapCastleStandardScheme extends org.apache.thrift.scheme.StandardScheme<PsCSAMinMapCastle> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PsCSAMinMapCastle struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // GARRISON_SERVER_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.garrisonServerId = iprot.readI32();
              struct.setGarrisonServerIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // META_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.metaId = iprot.readString();
              struct.setMetaIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // CSA_ATTACK_OCCUPY
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.csaAttackOccupy = iprot.readI64();
              struct.setCsaAttackOccupyIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // SETOUT_COUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.setoutCount = iprot.readI32();
              struct.setSetoutCountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PsCSAMinMapCastle struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.isSetGarrisonServerId()) {
        oprot.writeFieldBegin(GARRISON_SERVER_ID_FIELD_DESC);
        oprot.writeI32(struct.garrisonServerId);
        oprot.writeFieldEnd();
      }
      if (struct.metaId != null) {
        if (struct.isSetMetaId()) {
          oprot.writeFieldBegin(META_ID_FIELD_DESC);
          oprot.writeString(struct.metaId);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetCsaAttackOccupy()) {
        oprot.writeFieldBegin(CSA_ATTACK_OCCUPY_FIELD_DESC);
        oprot.writeI64(struct.csaAttackOccupy);
        oprot.writeFieldEnd();
      }
      if (struct.isSetSetoutCount()) {
        oprot.writeFieldBegin(SETOUT_COUNT_FIELD_DESC);
        oprot.writeI32(struct.setoutCount);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PsCSAMinMapCastleTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsCSAMinMapCastleTupleScheme getScheme() {
      return new PsCSAMinMapCastleTupleScheme();
    }
  }

  private static class PsCSAMinMapCastleTupleScheme extends org.apache.thrift.scheme.TupleScheme<PsCSAMinMapCastle> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PsCSAMinMapCastle struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetGarrisonServerId()) {
        optionals.set(0);
      }
      if (struct.isSetMetaId()) {
        optionals.set(1);
      }
      if (struct.isSetCsaAttackOccupy()) {
        optionals.set(2);
      }
      if (struct.isSetSetoutCount()) {
        optionals.set(3);
      }
      oprot.writeBitSet(optionals, 4);
      if (struct.isSetGarrisonServerId()) {
        oprot.writeI32(struct.garrisonServerId);
      }
      if (struct.isSetMetaId()) {
        oprot.writeString(struct.metaId);
      }
      if (struct.isSetCsaAttackOccupy()) {
        oprot.writeI64(struct.csaAttackOccupy);
      }
      if (struct.isSetSetoutCount()) {
        oprot.writeI32(struct.setoutCount);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PsCSAMinMapCastle struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(4);
      if (incoming.get(0)) {
        struct.garrisonServerId = iprot.readI32();
        struct.setGarrisonServerIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.metaId = iprot.readString();
        struct.setMetaIdIsSet(true);
      }
      if (incoming.get(2)) {
        struct.csaAttackOccupy = iprot.readI64();
        struct.setCsaAttackOccupyIsSet(true);
      }
      if (incoming.get(3)) {
        struct.setoutCount = iprot.readI32();
        struct.setSetoutCountIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

