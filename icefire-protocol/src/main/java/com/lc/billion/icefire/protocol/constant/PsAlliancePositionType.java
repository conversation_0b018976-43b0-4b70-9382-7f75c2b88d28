/**
 * Autogenerated by Thrift Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.constant;


/**
 * 联盟官职枚举
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public enum PsAlliancePositionType implements org.apache.thrift.TEnum {
  /**
   * 无官职
   */
  NONE(0),
  /**
   * 统帅
   */
  COMMANDER(1),
  /**
   * 招募官
   */
  RECRUITMENT(2),
  /**
   * 战略官
   */
  STRATEGIC(3),
  /**
   * 外使官
   */
  DIPLOMATIC(4),
  /**
   * 美人
   */
  BEAUTY(5);

  private final int value;

  private PsAlliancePositionType(int value) {
    this.value = value;
  }

  /**
   * Get the integer value of this enum value, as defined in the Thrift IDL.
   */
  public int getValue() {
    return value;
  }

  /**
   * Find a the enum type by its integer value, as defined in the Thrift IDL.
   * @return null if the value is not found.
   */
  @org.apache.thrift.annotation.Nullable
  public static PsAlliancePositionType findByValue(int value) { 
    switch (value) {
      case 0:
        return NONE;
      case 1:
        return COMMANDER;
      case 2:
        return RECRUITMENT;
      case 3:
        return STRATEGIC;
      case 4:
        return DIPLOMATIC;
      case 5:
        return BEAUTY;
      default:
        return null;
    }
  }
}
