/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 英雄属性升星
 * @Message(1659)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class CgHeroRankUpgrade implements org.apache.thrift.TBase<CgHeroRankUpgrade, CgHeroRankUpgrade._Fields>, java.io.Serializable, Cloneable, Comparable<CgHeroRankUpgrade> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("CgHeroRankUpgrade");

  private static final org.apache.thrift.protocol.TField HERO_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("HeroId", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField OLD_STAR_FIELD_DESC = new org.apache.thrift.protocol.TField("oldStar", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField NEW_STAR_FIELD_DESC = new org.apache.thrift.protocol.TField("newStar", org.apache.thrift.protocol.TType.I32, (short)3);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new CgHeroRankUpgradeStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new CgHeroRankUpgradeTupleSchemeFactory();

  /**
   * id
   */
  public @org.apache.thrift.annotation.Nullable java.lang.String HeroId; // required
  public int oldStar; // required
  public int newStar; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * id
     */
    HERO_ID((short)1, "HeroId"),
    OLD_STAR((short)2, "oldStar"),
    NEW_STAR((short)3, "newStar");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // HERO_ID
          return HERO_ID;
        case 2: // OLD_STAR
          return OLD_STAR;
        case 3: // NEW_STAR
          return NEW_STAR;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __OLDSTAR_ISSET_ID = 0;
  private static final int __NEWSTAR_ISSET_ID = 1;
  private byte __isset_bitfield = 0;
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.HERO_ID, new org.apache.thrift.meta_data.FieldMetaData("HeroId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.OLD_STAR, new org.apache.thrift.meta_data.FieldMetaData("oldStar", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.NEW_STAR, new org.apache.thrift.meta_data.FieldMetaData("newStar", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(CgHeroRankUpgrade.class, metaDataMap);
  }

  public CgHeroRankUpgrade() {
  }

  public CgHeroRankUpgrade(
    java.lang.String HeroId,
    int oldStar,
    int newStar)
  {
    this();
    this.HeroId = HeroId;
    this.oldStar = oldStar;
    setOldStarIsSet(true);
    this.newStar = newStar;
    setNewStarIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public CgHeroRankUpgrade(CgHeroRankUpgrade other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetHeroId()) {
      this.HeroId = other.HeroId;
    }
    this.oldStar = other.oldStar;
    this.newStar = other.newStar;
  }

  public CgHeroRankUpgrade deepCopy() {
    return new CgHeroRankUpgrade(this);
  }

  @Override
  public void clear() {
    this.HeroId = null;
    setOldStarIsSet(false);
    this.oldStar = 0;
    setNewStarIsSet(false);
    this.newStar = 0;
  }

  /**
   * id
   */
  @org.apache.thrift.annotation.Nullable
  public java.lang.String getHeroId() {
    return this.HeroId;
  }

  /**
   * id
   */
  public CgHeroRankUpgrade setHeroId(@org.apache.thrift.annotation.Nullable java.lang.String HeroId) {
    this.HeroId = HeroId;
    return this;
  }

  public void unsetHeroId() {
    this.HeroId = null;
  }

  /** Returns true if field HeroId is set (has been assigned a value) and false otherwise */
  public boolean isSetHeroId() {
    return this.HeroId != null;
  }

  public void setHeroIdIsSet(boolean value) {
    if (!value) {
      this.HeroId = null;
    }
  }

  public int getOldStar() {
    return this.oldStar;
  }

  public CgHeroRankUpgrade setOldStar(int oldStar) {
    this.oldStar = oldStar;
    setOldStarIsSet(true);
    return this;
  }

  public void unsetOldStar() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __OLDSTAR_ISSET_ID);
  }

  /** Returns true if field oldStar is set (has been assigned a value) and false otherwise */
  public boolean isSetOldStar() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __OLDSTAR_ISSET_ID);
  }

  public void setOldStarIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __OLDSTAR_ISSET_ID, value);
  }

  public int getNewStar() {
    return this.newStar;
  }

  public CgHeroRankUpgrade setNewStar(int newStar) {
    this.newStar = newStar;
    setNewStarIsSet(true);
    return this;
  }

  public void unsetNewStar() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __NEWSTAR_ISSET_ID);
  }

  /** Returns true if field newStar is set (has been assigned a value) and false otherwise */
  public boolean isSetNewStar() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __NEWSTAR_ISSET_ID);
  }

  public void setNewStarIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __NEWSTAR_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case HERO_ID:
      if (value == null) {
        unsetHeroId();
      } else {
        setHeroId((java.lang.String)value);
      }
      break;

    case OLD_STAR:
      if (value == null) {
        unsetOldStar();
      } else {
        setOldStar((java.lang.Integer)value);
      }
      break;

    case NEW_STAR:
      if (value == null) {
        unsetNewStar();
      } else {
        setNewStar((java.lang.Integer)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case HERO_ID:
      return getHeroId();

    case OLD_STAR:
      return getOldStar();

    case NEW_STAR:
      return getNewStar();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case HERO_ID:
      return isSetHeroId();
    case OLD_STAR:
      return isSetOldStar();
    case NEW_STAR:
      return isSetNewStar();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof CgHeroRankUpgrade)
      return this.equals((CgHeroRankUpgrade)that);
    return false;
  }

  public boolean equals(CgHeroRankUpgrade that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_HeroId = true && this.isSetHeroId();
    boolean that_present_HeroId = true && that.isSetHeroId();
    if (this_present_HeroId || that_present_HeroId) {
      if (!(this_present_HeroId && that_present_HeroId))
        return false;
      if (!this.HeroId.equals(that.HeroId))
        return false;
    }

    boolean this_present_oldStar = true;
    boolean that_present_oldStar = true;
    if (this_present_oldStar || that_present_oldStar) {
      if (!(this_present_oldStar && that_present_oldStar))
        return false;
      if (this.oldStar != that.oldStar)
        return false;
    }

    boolean this_present_newStar = true;
    boolean that_present_newStar = true;
    if (this_present_newStar || that_present_newStar) {
      if (!(this_present_newStar && that_present_newStar))
        return false;
      if (this.newStar != that.newStar)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetHeroId()) ? 131071 : 524287);
    if (isSetHeroId())
      hashCode = hashCode * 8191 + HeroId.hashCode();

    hashCode = hashCode * 8191 + oldStar;

    hashCode = hashCode * 8191 + newStar;

    return hashCode;
  }

  @Override
  public int compareTo(CgHeroRankUpgrade other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetHeroId(), other.isSetHeroId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetHeroId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.HeroId, other.HeroId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetOldStar(), other.isSetOldStar());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOldStar()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.oldStar, other.oldStar);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetNewStar(), other.isSetNewStar());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetNewStar()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.newStar, other.newStar);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("CgHeroRankUpgrade(");
    boolean first = true;

    sb.append("HeroId:");
    if (this.HeroId == null) {
      sb.append("null");
    } else {
      sb.append(this.HeroId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("oldStar:");
    sb.append(this.oldStar);
    first = false;
    if (!first) sb.append(", ");
    sb.append("newStar:");
    sb.append(this.newStar);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (HeroId == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'HeroId' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'oldStar' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'newStar' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class CgHeroRankUpgradeStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgHeroRankUpgradeStandardScheme getScheme() {
      return new CgHeroRankUpgradeStandardScheme();
    }
  }

  private static class CgHeroRankUpgradeStandardScheme extends org.apache.thrift.scheme.StandardScheme<CgHeroRankUpgrade> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, CgHeroRankUpgrade struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // HERO_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.HeroId = iprot.readString();
              struct.setHeroIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // OLD_STAR
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.oldStar = iprot.readI32();
              struct.setOldStarIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // NEW_STAR
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.newStar = iprot.readI32();
              struct.setNewStarIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetOldStar()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'oldStar' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetNewStar()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'newStar' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, CgHeroRankUpgrade struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.HeroId != null) {
        oprot.writeFieldBegin(HERO_ID_FIELD_DESC);
        oprot.writeString(struct.HeroId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(OLD_STAR_FIELD_DESC);
      oprot.writeI32(struct.oldStar);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(NEW_STAR_FIELD_DESC);
      oprot.writeI32(struct.newStar);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class CgHeroRankUpgradeTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgHeroRankUpgradeTupleScheme getScheme() {
      return new CgHeroRankUpgradeTupleScheme();
    }
  }

  private static class CgHeroRankUpgradeTupleScheme extends org.apache.thrift.scheme.TupleScheme<CgHeroRankUpgrade> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, CgHeroRankUpgrade struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeString(struct.HeroId);
      oprot.writeI32(struct.oldStar);
      oprot.writeI32(struct.newStar);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, CgHeroRankUpgrade struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.HeroId = iprot.readString();
      struct.setHeroIdIsSet(true);
      struct.oldStar = iprot.readI32();
      struct.setOldStarIsSet(true);
      struct.newStar = iprot.readI32();
      struct.setNewStarIsSet(true);
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

