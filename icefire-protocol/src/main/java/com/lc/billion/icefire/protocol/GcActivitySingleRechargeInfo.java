/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcActivitySingleRechargeInfo implements org.apache.thrift.TBase<GcActivitySingleRechargeInfo, GcActivitySingleRechargeInfo._Fields>, java.io.Serializable, Cloneable, Comparable<GcActivitySingleRechargeInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcActivitySingleRechargeInfo");

  private static final org.apache.thrift.protocol.TField ACTIVITY_META_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("activityMetaId", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField GOAL_INFOS_FIELD_DESC = new org.apache.thrift.protocol.TField("goalInfos", org.apache.thrift.protocol.TType.LIST, (short)2);
  private static final org.apache.thrift.protocol.TField METAS_FIELD_DESC = new org.apache.thrift.protocol.TField("metas", org.apache.thrift.protocol.TType.LIST, (short)3);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcActivitySingleRechargeInfoStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcActivitySingleRechargeInfoTupleSchemeFactory();

  /**
   * 活动metaId *
   */
  public @org.apache.thrift.annotation.Nullable java.lang.String activityMetaId; // required
  /**
   * 完成动态数据 *
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo> goalInfos; // required
  /**
   * 静态数据 *
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsSingleRechargeMetaInfo> metas; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 活动metaId *
     */
    ACTIVITY_META_ID((short)1, "activityMetaId"),
    /**
     * 完成动态数据 *
     */
    GOAL_INFOS((short)2, "goalInfos"),
    /**
     * 静态数据 *
     */
    METAS((short)3, "metas");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ACTIVITY_META_ID
          return ACTIVITY_META_ID;
        case 2: // GOAL_INFOS
          return GOAL_INFOS;
        case 3: // METAS
          return METAS;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final _Fields optionals[] = {_Fields.METAS};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ACTIVITY_META_ID, new org.apache.thrift.meta_data.FieldMetaData("activityMetaId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.GOAL_INFOS, new org.apache.thrift.meta_data.FieldMetaData("goalInfos", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo.class))));
    tmpMap.put(_Fields.METAS, new org.apache.thrift.meta_data.FieldMetaData("metas", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsSingleRechargeMetaInfo.class))));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcActivitySingleRechargeInfo.class, metaDataMap);
  }

  public GcActivitySingleRechargeInfo() {
  }

  public GcActivitySingleRechargeInfo(
    java.lang.String activityMetaId,
    java.util.List<com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo> goalInfos)
  {
    this();
    this.activityMetaId = activityMetaId;
    this.goalInfos = goalInfos;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcActivitySingleRechargeInfo(GcActivitySingleRechargeInfo other) {
    if (other.isSetActivityMetaId()) {
      this.activityMetaId = other.activityMetaId;
    }
    if (other.isSetGoalInfos()) {
      java.util.List<com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo> __this__goalInfos = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo>(other.goalInfos.size());
      for (com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo other_element : other.goalInfos) {
        __this__goalInfos.add(new com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo(other_element));
      }
      this.goalInfos = __this__goalInfos;
    }
    if (other.isSetMetas()) {
      java.util.List<com.lc.billion.icefire.protocol.structure.PsSingleRechargeMetaInfo> __this__metas = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSingleRechargeMetaInfo>(other.metas.size());
      for (com.lc.billion.icefire.protocol.structure.PsSingleRechargeMetaInfo other_element : other.metas) {
        __this__metas.add(new com.lc.billion.icefire.protocol.structure.PsSingleRechargeMetaInfo(other_element));
      }
      this.metas = __this__metas;
    }
  }

  public GcActivitySingleRechargeInfo deepCopy() {
    return new GcActivitySingleRechargeInfo(this);
  }

  @Override
  public void clear() {
    this.activityMetaId = null;
    this.goalInfos = null;
    this.metas = null;
  }

  /**
   * 活动metaId *
   */
  @org.apache.thrift.annotation.Nullable
  public java.lang.String getActivityMetaId() {
    return this.activityMetaId;
  }

  /**
   * 活动metaId *
   */
  public GcActivitySingleRechargeInfo setActivityMetaId(@org.apache.thrift.annotation.Nullable java.lang.String activityMetaId) {
    this.activityMetaId = activityMetaId;
    return this;
  }

  public void unsetActivityMetaId() {
    this.activityMetaId = null;
  }

  /** Returns true if field activityMetaId is set (has been assigned a value) and false otherwise */
  public boolean isSetActivityMetaId() {
    return this.activityMetaId != null;
  }

  public void setActivityMetaIdIsSet(boolean value) {
    if (!value) {
      this.activityMetaId = null;
    }
  }

  public int getGoalInfosSize() {
    return (this.goalInfos == null) ? 0 : this.goalInfos.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo> getGoalInfosIterator() {
    return (this.goalInfos == null) ? null : this.goalInfos.iterator();
  }

  public void addToGoalInfos(com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo elem) {
    if (this.goalInfos == null) {
      this.goalInfos = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo>();
    }
    this.goalInfos.add(elem);
  }

  /**
   * 完成动态数据 *
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo> getGoalInfos() {
    return this.goalInfos;
  }

  /**
   * 完成动态数据 *
   */
  public GcActivitySingleRechargeInfo setGoalInfos(@org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo> goalInfos) {
    this.goalInfos = goalInfos;
    return this;
  }

  public void unsetGoalInfos() {
    this.goalInfos = null;
  }

  /** Returns true if field goalInfos is set (has been assigned a value) and false otherwise */
  public boolean isSetGoalInfos() {
    return this.goalInfos != null;
  }

  public void setGoalInfosIsSet(boolean value) {
    if (!value) {
      this.goalInfos = null;
    }
  }

  public int getMetasSize() {
    return (this.metas == null) ? 0 : this.metas.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<com.lc.billion.icefire.protocol.structure.PsSingleRechargeMetaInfo> getMetasIterator() {
    return (this.metas == null) ? null : this.metas.iterator();
  }

  public void addToMetas(com.lc.billion.icefire.protocol.structure.PsSingleRechargeMetaInfo elem) {
    if (this.metas == null) {
      this.metas = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSingleRechargeMetaInfo>();
    }
    this.metas.add(elem);
  }

  /**
   * 静态数据 *
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<com.lc.billion.icefire.protocol.structure.PsSingleRechargeMetaInfo> getMetas() {
    return this.metas;
  }

  /**
   * 静态数据 *
   */
  public GcActivitySingleRechargeInfo setMetas(@org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsSingleRechargeMetaInfo> metas) {
    this.metas = metas;
    return this;
  }

  public void unsetMetas() {
    this.metas = null;
  }

  /** Returns true if field metas is set (has been assigned a value) and false otherwise */
  public boolean isSetMetas() {
    return this.metas != null;
  }

  public void setMetasIsSet(boolean value) {
    if (!value) {
      this.metas = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ACTIVITY_META_ID:
      if (value == null) {
        unsetActivityMetaId();
      } else {
        setActivityMetaId((java.lang.String)value);
      }
      break;

    case GOAL_INFOS:
      if (value == null) {
        unsetGoalInfos();
      } else {
        setGoalInfos((java.util.List<com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo>)value);
      }
      break;

    case METAS:
      if (value == null) {
        unsetMetas();
      } else {
        setMetas((java.util.List<com.lc.billion.icefire.protocol.structure.PsSingleRechargeMetaInfo>)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ACTIVITY_META_ID:
      return getActivityMetaId();

    case GOAL_INFOS:
      return getGoalInfos();

    case METAS:
      return getMetas();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ACTIVITY_META_ID:
      return isSetActivityMetaId();
    case GOAL_INFOS:
      return isSetGoalInfos();
    case METAS:
      return isSetMetas();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcActivitySingleRechargeInfo)
      return this.equals((GcActivitySingleRechargeInfo)that);
    return false;
  }

  public boolean equals(GcActivitySingleRechargeInfo that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_activityMetaId = true && this.isSetActivityMetaId();
    boolean that_present_activityMetaId = true && that.isSetActivityMetaId();
    if (this_present_activityMetaId || that_present_activityMetaId) {
      if (!(this_present_activityMetaId && that_present_activityMetaId))
        return false;
      if (!this.activityMetaId.equals(that.activityMetaId))
        return false;
    }

    boolean this_present_goalInfos = true && this.isSetGoalInfos();
    boolean that_present_goalInfos = true && that.isSetGoalInfos();
    if (this_present_goalInfos || that_present_goalInfos) {
      if (!(this_present_goalInfos && that_present_goalInfos))
        return false;
      if (!this.goalInfos.equals(that.goalInfos))
        return false;
    }

    boolean this_present_metas = true && this.isSetMetas();
    boolean that_present_metas = true && that.isSetMetas();
    if (this_present_metas || that_present_metas) {
      if (!(this_present_metas && that_present_metas))
        return false;
      if (!this.metas.equals(that.metas))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetActivityMetaId()) ? 131071 : 524287);
    if (isSetActivityMetaId())
      hashCode = hashCode * 8191 + activityMetaId.hashCode();

    hashCode = hashCode * 8191 + ((isSetGoalInfos()) ? 131071 : 524287);
    if (isSetGoalInfos())
      hashCode = hashCode * 8191 + goalInfos.hashCode();

    hashCode = hashCode * 8191 + ((isSetMetas()) ? 131071 : 524287);
    if (isSetMetas())
      hashCode = hashCode * 8191 + metas.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(GcActivitySingleRechargeInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetActivityMetaId(), other.isSetActivityMetaId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActivityMetaId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.activityMetaId, other.activityMetaId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetGoalInfos(), other.isSetGoalInfos());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGoalInfos()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.goalInfos, other.goalInfos);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetMetas(), other.isSetMetas());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMetas()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.metas, other.metas);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcActivitySingleRechargeInfo(");
    boolean first = true;

    sb.append("activityMetaId:");
    if (this.activityMetaId == null) {
      sb.append("null");
    } else {
      sb.append(this.activityMetaId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("goalInfos:");
    if (this.goalInfos == null) {
      sb.append("null");
    } else {
      sb.append(this.goalInfos);
    }
    first = false;
    if (isSetMetas()) {
      if (!first) sb.append(", ");
      sb.append("metas:");
      if (this.metas == null) {
        sb.append("null");
      } else {
        sb.append(this.metas);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (activityMetaId == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'activityMetaId' was not present! Struct: " + toString());
    }
    if (goalInfos == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'goalInfos' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcActivitySingleRechargeInfoStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcActivitySingleRechargeInfoStandardScheme getScheme() {
      return new GcActivitySingleRechargeInfoStandardScheme();
    }
  }

  private static class GcActivitySingleRechargeInfoStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcActivitySingleRechargeInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcActivitySingleRechargeInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ACTIVITY_META_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.activityMetaId = iprot.readString();
              struct.setActivityMetaIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // GOAL_INFOS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.goalInfos = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo>(_list0.size);
                @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo();
                  _elem1.read(iprot);
                  struct.goalInfos.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setGoalInfosIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // METAS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list3 = iprot.readListBegin();
                struct.metas = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSingleRechargeMetaInfo>(_list3.size);
                @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsSingleRechargeMetaInfo _elem4;
                for (int _i5 = 0; _i5 < _list3.size; ++_i5)
                {
                  _elem4 = new com.lc.billion.icefire.protocol.structure.PsSingleRechargeMetaInfo();
                  _elem4.read(iprot);
                  struct.metas.add(_elem4);
                }
                iprot.readListEnd();
              }
              struct.setMetasIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcActivitySingleRechargeInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.activityMetaId != null) {
        oprot.writeFieldBegin(ACTIVITY_META_ID_FIELD_DESC);
        oprot.writeString(struct.activityMetaId);
        oprot.writeFieldEnd();
      }
      if (struct.goalInfos != null) {
        oprot.writeFieldBegin(GOAL_INFOS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.goalInfos.size()));
          for (com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo _iter6 : struct.goalInfos)
          {
            _iter6.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.metas != null) {
        if (struct.isSetMetas()) {
          oprot.writeFieldBegin(METAS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.metas.size()));
            for (com.lc.billion.icefire.protocol.structure.PsSingleRechargeMetaInfo _iter7 : struct.metas)
            {
              _iter7.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcActivitySingleRechargeInfoTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcActivitySingleRechargeInfoTupleScheme getScheme() {
      return new GcActivitySingleRechargeInfoTupleScheme();
    }
  }

  private static class GcActivitySingleRechargeInfoTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcActivitySingleRechargeInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcActivitySingleRechargeInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeString(struct.activityMetaId);
      {
        oprot.writeI32(struct.goalInfos.size());
        for (com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo _iter8 : struct.goalInfos)
        {
          _iter8.write(oprot);
        }
      }
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetMetas()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetMetas()) {
        {
          oprot.writeI32(struct.metas.size());
          for (com.lc.billion.icefire.protocol.structure.PsSingleRechargeMetaInfo _iter9 : struct.metas)
          {
            _iter9.write(oprot);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcActivitySingleRechargeInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.activityMetaId = iprot.readString();
      struct.setActivityMetaIdIsSet(true);
      {
        org.apache.thrift.protocol.TList _list10 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
        struct.goalInfos = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo>(_list10.size);
        @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo _elem11;
        for (int _i12 = 0; _i12 < _list10.size; ++_i12)
        {
          _elem11 = new com.lc.billion.icefire.protocol.structure.PsActivityGoalInfo();
          _elem11.read(iprot);
          struct.goalInfos.add(_elem11);
        }
      }
      struct.setGoalInfosIsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TList _list13 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
          struct.metas = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSingleRechargeMetaInfo>(_list13.size);
          @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsSingleRechargeMetaInfo _elem14;
          for (int _i15 = 0; _i15 < _list13.size; ++_i15)
          {
            _elem14 = new com.lc.billion.icefire.protocol.structure.PsSingleRechargeMetaInfo();
            _elem14.read(iprot);
            struct.metas.add(_elem14);
          }
        }
        struct.setMetasIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

