/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 收集建筑资源
 * @Message(220)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class CgBuildingCollect implements org.apache.thrift.TBase<CgBuildingCollect, CgBuildingCollect._Fields>, java.io.Serializable, Cloneable, Comparable<CgBuildingCollect> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("CgBuildingCollect");

  private static final org.apache.thrift.protocol.TField CITY_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("cityId", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField BUILDING_GROUP_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("buildingGroupId", org.apache.thrift.protocol.TType.I32, (short)2);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new CgBuildingCollectStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new CgBuildingCollectTupleSchemeFactory();

  /**
   * 城市id
   */
  public @org.apache.thrift.annotation.Nullable java.lang.String cityId; // required
  /**
   * 建筑配置id
   */
  public int buildingGroupId; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 城市id
     */
    CITY_ID((short)1, "cityId"),
    /**
     * 建筑配置id
     */
    BUILDING_GROUP_ID((short)2, "buildingGroupId");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // CITY_ID
          return CITY_ID;
        case 2: // BUILDING_GROUP_ID
          return BUILDING_GROUP_ID;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __BUILDINGGROUPID_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.CITY_ID, new org.apache.thrift.meta_data.FieldMetaData("cityId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.BUILDING_GROUP_ID, new org.apache.thrift.meta_data.FieldMetaData("buildingGroupId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(CgBuildingCollect.class, metaDataMap);
  }

  public CgBuildingCollect() {
  }

  public CgBuildingCollect(
    java.lang.String cityId,
    int buildingGroupId)
  {
    this();
    this.cityId = cityId;
    this.buildingGroupId = buildingGroupId;
    setBuildingGroupIdIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public CgBuildingCollect(CgBuildingCollect other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetCityId()) {
      this.cityId = other.cityId;
    }
    this.buildingGroupId = other.buildingGroupId;
  }

  public CgBuildingCollect deepCopy() {
    return new CgBuildingCollect(this);
  }

  @Override
  public void clear() {
    this.cityId = null;
    setBuildingGroupIdIsSet(false);
    this.buildingGroupId = 0;
  }

  /**
   * 城市id
   */
  @org.apache.thrift.annotation.Nullable
  public java.lang.String getCityId() {
    return this.cityId;
  }

  /**
   * 城市id
   */
  public CgBuildingCollect setCityId(@org.apache.thrift.annotation.Nullable java.lang.String cityId) {
    this.cityId = cityId;
    return this;
  }

  public void unsetCityId() {
    this.cityId = null;
  }

  /** Returns true if field cityId is set (has been assigned a value) and false otherwise */
  public boolean isSetCityId() {
    return this.cityId != null;
  }

  public void setCityIdIsSet(boolean value) {
    if (!value) {
      this.cityId = null;
    }
  }

  /**
   * 建筑配置id
   */
  public int getBuildingGroupId() {
    return this.buildingGroupId;
  }

  /**
   * 建筑配置id
   */
  public CgBuildingCollect setBuildingGroupId(int buildingGroupId) {
    this.buildingGroupId = buildingGroupId;
    setBuildingGroupIdIsSet(true);
    return this;
  }

  public void unsetBuildingGroupId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __BUILDINGGROUPID_ISSET_ID);
  }

  /** Returns true if field buildingGroupId is set (has been assigned a value) and false otherwise */
  public boolean isSetBuildingGroupId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __BUILDINGGROUPID_ISSET_ID);
  }

  public void setBuildingGroupIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __BUILDINGGROUPID_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case CITY_ID:
      if (value == null) {
        unsetCityId();
      } else {
        setCityId((java.lang.String)value);
      }
      break;

    case BUILDING_GROUP_ID:
      if (value == null) {
        unsetBuildingGroupId();
      } else {
        setBuildingGroupId((java.lang.Integer)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case CITY_ID:
      return getCityId();

    case BUILDING_GROUP_ID:
      return getBuildingGroupId();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case CITY_ID:
      return isSetCityId();
    case BUILDING_GROUP_ID:
      return isSetBuildingGroupId();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof CgBuildingCollect)
      return this.equals((CgBuildingCollect)that);
    return false;
  }

  public boolean equals(CgBuildingCollect that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_cityId = true && this.isSetCityId();
    boolean that_present_cityId = true && that.isSetCityId();
    if (this_present_cityId || that_present_cityId) {
      if (!(this_present_cityId && that_present_cityId))
        return false;
      if (!this.cityId.equals(that.cityId))
        return false;
    }

    boolean this_present_buildingGroupId = true;
    boolean that_present_buildingGroupId = true;
    if (this_present_buildingGroupId || that_present_buildingGroupId) {
      if (!(this_present_buildingGroupId && that_present_buildingGroupId))
        return false;
      if (this.buildingGroupId != that.buildingGroupId)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetCityId()) ? 131071 : 524287);
    if (isSetCityId())
      hashCode = hashCode * 8191 + cityId.hashCode();

    hashCode = hashCode * 8191 + buildingGroupId;

    return hashCode;
  }

  @Override
  public int compareTo(CgBuildingCollect other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetCityId(), other.isSetCityId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCityId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.cityId, other.cityId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetBuildingGroupId(), other.isSetBuildingGroupId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBuildingGroupId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.buildingGroupId, other.buildingGroupId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("CgBuildingCollect(");
    boolean first = true;

    sb.append("cityId:");
    if (this.cityId == null) {
      sb.append("null");
    } else {
      sb.append(this.cityId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("buildingGroupId:");
    sb.append(this.buildingGroupId);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    if (cityId == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'cityId' was not present! Struct: " + toString());
    }
    // alas, we cannot check 'buildingGroupId' because it's a primitive and you chose the non-beans generator.
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class CgBuildingCollectStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgBuildingCollectStandardScheme getScheme() {
      return new CgBuildingCollectStandardScheme();
    }
  }

  private static class CgBuildingCollectStandardScheme extends org.apache.thrift.scheme.StandardScheme<CgBuildingCollect> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, CgBuildingCollect struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // CITY_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.cityId = iprot.readString();
              struct.setCityIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // BUILDING_GROUP_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.buildingGroupId = iprot.readI32();
              struct.setBuildingGroupIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetBuildingGroupId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'buildingGroupId' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, CgBuildingCollect struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.cityId != null) {
        oprot.writeFieldBegin(CITY_ID_FIELD_DESC);
        oprot.writeString(struct.cityId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(BUILDING_GROUP_ID_FIELD_DESC);
      oprot.writeI32(struct.buildingGroupId);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class CgBuildingCollectTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgBuildingCollectTupleScheme getScheme() {
      return new CgBuildingCollectTupleScheme();
    }
  }

  private static class CgBuildingCollectTupleScheme extends org.apache.thrift.scheme.TupleScheme<CgBuildingCollect> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, CgBuildingCollect struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeString(struct.cityId);
      oprot.writeI32(struct.buildingGroupId);
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, CgBuildingCollect struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.cityId = iprot.readString();
      struct.setCityIdIsSet(true);
      struct.buildingGroupId = iprot.readI32();
      struct.setBuildingGroupIdIsSet(true);
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

