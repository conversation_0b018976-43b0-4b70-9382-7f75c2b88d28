/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.structure;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 官员信息
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class PsOfficial implements org.apache.thrift.TBase<PsOfficial, PsOfficial._Fields>, java.io.Serializable, Cloneable, Comparable<PsOfficial> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PsOfficial");

  private static final org.apache.thrift.protocol.TField META_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("metaId", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField ROLE_FIELD_DESC = new org.apache.thrift.protocol.TField("role", org.apache.thrift.protocol.TType.STRUCT, (short)2);
  private static final org.apache.thrift.protocol.TField START_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("startTime", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField SERVER_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("serverId", org.apache.thrift.protocol.TType.I32, (short)4);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new PsOfficialStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new PsOfficialTupleSchemeFactory();

  /**
   * 官员metaId *
   */
  public @org.apache.thrift.annotation.Nullable java.lang.String metaId; // optional
  /**
   * 玩家信息 *
   */
  public @org.apache.thrift.annotation.Nullable PsRoleInfo role; // optional
  /**
   * 任命时间 *
   */
  public long startTime; // optional
  /**
   * 服务器id *
   */
  public int serverId; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 官员metaId *
     */
    META_ID((short)1, "metaId"),
    /**
     * 玩家信息 *
     */
    ROLE((short)2, "role"),
    /**
     * 任命时间 *
     */
    START_TIME((short)3, "startTime"),
    /**
     * 服务器id *
     */
    SERVER_ID((short)4, "serverId");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // META_ID
          return META_ID;
        case 2: // ROLE
          return ROLE;
        case 3: // START_TIME
          return START_TIME;
        case 4: // SERVER_ID
          return SERVER_ID;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __STARTTIME_ISSET_ID = 0;
  private static final int __SERVERID_ISSET_ID = 1;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.META_ID,_Fields.ROLE,_Fields.START_TIME,_Fields.SERVER_ID};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.META_ID, new org.apache.thrift.meta_data.FieldMetaData("metaId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ROLE, new org.apache.thrift.meta_data.FieldMetaData("role", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, PsRoleInfo.class)));
    tmpMap.put(_Fields.START_TIME, new org.apache.thrift.meta_data.FieldMetaData("startTime", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SERVER_ID, new org.apache.thrift.meta_data.FieldMetaData("serverId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PsOfficial.class, metaDataMap);
  }

  public PsOfficial() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PsOfficial(PsOfficial other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetMetaId()) {
      this.metaId = other.metaId;
    }
    if (other.isSetRole()) {
      this.role = new PsRoleInfo(other.role);
    }
    this.startTime = other.startTime;
    this.serverId = other.serverId;
  }

  public PsOfficial deepCopy() {
    return new PsOfficial(this);
  }

  @Override
  public void clear() {
    this.metaId = null;
    this.role = null;
    setStartTimeIsSet(false);
    this.startTime = 0;
    setServerIdIsSet(false);
    this.serverId = 0;
  }

  /**
   * 官员metaId *
   */
  @org.apache.thrift.annotation.Nullable
  public java.lang.String getMetaId() {
    return this.metaId;
  }

  /**
   * 官员metaId *
   */
  public PsOfficial setMetaId(@org.apache.thrift.annotation.Nullable java.lang.String metaId) {
    this.metaId = metaId;
    return this;
  }

  public void unsetMetaId() {
    this.metaId = null;
  }

  /** Returns true if field metaId is set (has been assigned a value) and false otherwise */
  public boolean isSetMetaId() {
    return this.metaId != null;
  }

  public void setMetaIdIsSet(boolean value) {
    if (!value) {
      this.metaId = null;
    }
  }

  /**
   * 玩家信息 *
   */
  @org.apache.thrift.annotation.Nullable
  public PsRoleInfo getRole() {
    return this.role;
  }

  /**
   * 玩家信息 *
   */
  public PsOfficial setRole(@org.apache.thrift.annotation.Nullable PsRoleInfo role) {
    this.role = role;
    return this;
  }

  public void unsetRole() {
    this.role = null;
  }

  /** Returns true if field role is set (has been assigned a value) and false otherwise */
  public boolean isSetRole() {
    return this.role != null;
  }

  public void setRoleIsSet(boolean value) {
    if (!value) {
      this.role = null;
    }
  }

  /**
   * 任命时间 *
   */
  public long getStartTime() {
    return this.startTime;
  }

  /**
   * 任命时间 *
   */
  public PsOfficial setStartTime(long startTime) {
    this.startTime = startTime;
    setStartTimeIsSet(true);
    return this;
  }

  public void unsetStartTime() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __STARTTIME_ISSET_ID);
  }

  /** Returns true if field startTime is set (has been assigned a value) and false otherwise */
  public boolean isSetStartTime() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __STARTTIME_ISSET_ID);
  }

  public void setStartTimeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __STARTTIME_ISSET_ID, value);
  }

  /**
   * 服务器id *
   */
  public int getServerId() {
    return this.serverId;
  }

  /**
   * 服务器id *
   */
  public PsOfficial setServerId(int serverId) {
    this.serverId = serverId;
    setServerIdIsSet(true);
    return this;
  }

  public void unsetServerId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __SERVERID_ISSET_ID);
  }

  /** Returns true if field serverId is set (has been assigned a value) and false otherwise */
  public boolean isSetServerId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __SERVERID_ISSET_ID);
  }

  public void setServerIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __SERVERID_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case META_ID:
      if (value == null) {
        unsetMetaId();
      } else {
        setMetaId((java.lang.String)value);
      }
      break;

    case ROLE:
      if (value == null) {
        unsetRole();
      } else {
        setRole((PsRoleInfo)value);
      }
      break;

    case START_TIME:
      if (value == null) {
        unsetStartTime();
      } else {
        setStartTime((java.lang.Long)value);
      }
      break;

    case SERVER_ID:
      if (value == null) {
        unsetServerId();
      } else {
        setServerId((java.lang.Integer)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case META_ID:
      return getMetaId();

    case ROLE:
      return getRole();

    case START_TIME:
      return getStartTime();

    case SERVER_ID:
      return getServerId();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case META_ID:
      return isSetMetaId();
    case ROLE:
      return isSetRole();
    case START_TIME:
      return isSetStartTime();
    case SERVER_ID:
      return isSetServerId();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof PsOfficial)
      return this.equals((PsOfficial)that);
    return false;
  }

  public boolean equals(PsOfficial that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_metaId = true && this.isSetMetaId();
    boolean that_present_metaId = true && that.isSetMetaId();
    if (this_present_metaId || that_present_metaId) {
      if (!(this_present_metaId && that_present_metaId))
        return false;
      if (!this.metaId.equals(that.metaId))
        return false;
    }

    boolean this_present_role = true && this.isSetRole();
    boolean that_present_role = true && that.isSetRole();
    if (this_present_role || that_present_role) {
      if (!(this_present_role && that_present_role))
        return false;
      if (!this.role.equals(that.role))
        return false;
    }

    boolean this_present_startTime = true && this.isSetStartTime();
    boolean that_present_startTime = true && that.isSetStartTime();
    if (this_present_startTime || that_present_startTime) {
      if (!(this_present_startTime && that_present_startTime))
        return false;
      if (this.startTime != that.startTime)
        return false;
    }

    boolean this_present_serverId = true && this.isSetServerId();
    boolean that_present_serverId = true && that.isSetServerId();
    if (this_present_serverId || that_present_serverId) {
      if (!(this_present_serverId && that_present_serverId))
        return false;
      if (this.serverId != that.serverId)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetMetaId()) ? 131071 : 524287);
    if (isSetMetaId())
      hashCode = hashCode * 8191 + metaId.hashCode();

    hashCode = hashCode * 8191 + ((isSetRole()) ? 131071 : 524287);
    if (isSetRole())
      hashCode = hashCode * 8191 + role.hashCode();

    hashCode = hashCode * 8191 + ((isSetStartTime()) ? 131071 : 524287);
    if (isSetStartTime())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(startTime);

    hashCode = hashCode * 8191 + ((isSetServerId()) ? 131071 : 524287);
    if (isSetServerId())
      hashCode = hashCode * 8191 + serverId;

    return hashCode;
  }

  @Override
  public int compareTo(PsOfficial other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetMetaId(), other.isSetMetaId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMetaId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.metaId, other.metaId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetRole(), other.isSetRole());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRole()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.role, other.role);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetStartTime(), other.isSetStartTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStartTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.startTime, other.startTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetServerId(), other.isSetServerId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetServerId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.serverId, other.serverId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("PsOfficial(");
    boolean first = true;

    if (isSetMetaId()) {
      sb.append("metaId:");
      if (this.metaId == null) {
        sb.append("null");
      } else {
        sb.append(this.metaId);
      }
      first = false;
    }
    if (isSetRole()) {
      if (!first) sb.append(", ");
      sb.append("role:");
      if (this.role == null) {
        sb.append("null");
      } else {
        sb.append(this.role);
      }
      first = false;
    }
    if (isSetStartTime()) {
      if (!first) sb.append(", ");
      sb.append("startTime:");
      sb.append(this.startTime);
      first = false;
    }
    if (isSetServerId()) {
      if (!first) sb.append(", ");
      sb.append("serverId:");
      sb.append(this.serverId);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
    if (role != null) {
      role.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PsOfficialStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsOfficialStandardScheme getScheme() {
      return new PsOfficialStandardScheme();
    }
  }

  private static class PsOfficialStandardScheme extends org.apache.thrift.scheme.StandardScheme<PsOfficial> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PsOfficial struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // META_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.metaId = iprot.readString();
              struct.setMetaIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // ROLE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.role = new PsRoleInfo();
              struct.role.read(iprot);
              struct.setRoleIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // START_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.startTime = iprot.readI64();
              struct.setStartTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // SERVER_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.serverId = iprot.readI32();
              struct.setServerIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PsOfficial struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.metaId != null) {
        if (struct.isSetMetaId()) {
          oprot.writeFieldBegin(META_ID_FIELD_DESC);
          oprot.writeString(struct.metaId);
          oprot.writeFieldEnd();
        }
      }
      if (struct.role != null) {
        if (struct.isSetRole()) {
          oprot.writeFieldBegin(ROLE_FIELD_DESC);
          struct.role.write(oprot);
          oprot.writeFieldEnd();
        }
      }
      if (struct.isSetStartTime()) {
        oprot.writeFieldBegin(START_TIME_FIELD_DESC);
        oprot.writeI64(struct.startTime);
        oprot.writeFieldEnd();
      }
      if (struct.isSetServerId()) {
        oprot.writeFieldBegin(SERVER_ID_FIELD_DESC);
        oprot.writeI32(struct.serverId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PsOfficialTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsOfficialTupleScheme getScheme() {
      return new PsOfficialTupleScheme();
    }
  }

  private static class PsOfficialTupleScheme extends org.apache.thrift.scheme.TupleScheme<PsOfficial> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PsOfficial struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetMetaId()) {
        optionals.set(0);
      }
      if (struct.isSetRole()) {
        optionals.set(1);
      }
      if (struct.isSetStartTime()) {
        optionals.set(2);
      }
      if (struct.isSetServerId()) {
        optionals.set(3);
      }
      oprot.writeBitSet(optionals, 4);
      if (struct.isSetMetaId()) {
        oprot.writeString(struct.metaId);
      }
      if (struct.isSetRole()) {
        struct.role.write(oprot);
      }
      if (struct.isSetStartTime()) {
        oprot.writeI64(struct.startTime);
      }
      if (struct.isSetServerId()) {
        oprot.writeI32(struct.serverId);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PsOfficial struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(4);
      if (incoming.get(0)) {
        struct.metaId = iprot.readString();
        struct.setMetaIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.role = new PsRoleInfo();
        struct.role.read(iprot);
        struct.setRoleIsSet(true);
      }
      if (incoming.get(2)) {
        struct.startTime = iprot.readI64();
        struct.setStartTimeIsSet(true);
      }
      if (incoming.get(3)) {
        struct.serverId = iprot.readI32();
        struct.setServerIdIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

