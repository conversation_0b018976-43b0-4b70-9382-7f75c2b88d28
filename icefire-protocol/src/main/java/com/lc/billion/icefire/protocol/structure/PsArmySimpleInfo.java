/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.structure;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 玩家军队简单信息
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class PsArmySimpleInfo implements org.apache.thrift.TBase<PsArmySimpleInfo, PsArmySimpleInfo._Fields>, java.io.Serializable, Cloneable, Comparable<PsArmySimpleInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PsArmySimpleInfo");

  private static final org.apache.thrift.protocol.TField HERO_INFO_FIELD_DESC = new org.apache.thrift.protocol.TField("heroInfo", org.apache.thrift.protocol.TType.LIST, (short)1);
  private static final org.apache.thrift.protocol.TField SOLDIERS_FIELD_DESC = new org.apache.thrift.protocol.TField("soldiers", org.apache.thrift.protocol.TType.MAP, (short)2);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new PsArmySimpleInfoStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new PsArmySimpleInfoTupleSchemeFactory();

  /**
   * 英雄 *
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<PsHeroSimpleInfo> heroInfo; // optional
  /**
   * 编队士兵列表
   */
  public @org.apache.thrift.annotation.Nullable java.util.Map<java.lang.String,java.lang.Integer> soldiers; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 英雄 *
     */
    HERO_INFO((short)1, "heroInfo"),
    /**
     * 编队士兵列表
     */
    SOLDIERS((short)2, "soldiers");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // HERO_INFO
          return HERO_INFO;
        case 2: // SOLDIERS
          return SOLDIERS;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final _Fields optionals[] = {_Fields.HERO_INFO,_Fields.SOLDIERS};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.HERO_INFO, new org.apache.thrift.meta_data.FieldMetaData("heroInfo", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, PsHeroSimpleInfo.class))));
    tmpMap.put(_Fields.SOLDIERS, new org.apache.thrift.meta_data.FieldMetaData("soldiers", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32))));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PsArmySimpleInfo.class, metaDataMap);
  }

  public PsArmySimpleInfo() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PsArmySimpleInfo(PsArmySimpleInfo other) {
    if (other.isSetHeroInfo()) {
      java.util.List<PsHeroSimpleInfo> __this__heroInfo = new java.util.ArrayList<PsHeroSimpleInfo>(other.heroInfo.size());
      for (PsHeroSimpleInfo other_element : other.heroInfo) {
        __this__heroInfo.add(new PsHeroSimpleInfo(other_element));
      }
      this.heroInfo = __this__heroInfo;
    }
    if (other.isSetSoldiers()) {
      java.util.Map<java.lang.String,java.lang.Integer> __this__soldiers = new java.util.HashMap<java.lang.String,java.lang.Integer>(other.soldiers);
      this.soldiers = __this__soldiers;
    }
  }

  public PsArmySimpleInfo deepCopy() {
    return new PsArmySimpleInfo(this);
  }

  @Override
  public void clear() {
    this.heroInfo = null;
    this.soldiers = null;
  }

  public int getHeroInfoSize() {
    return (this.heroInfo == null) ? 0 : this.heroInfo.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<PsHeroSimpleInfo> getHeroInfoIterator() {
    return (this.heroInfo == null) ? null : this.heroInfo.iterator();
  }

  public void addToHeroInfo(PsHeroSimpleInfo elem) {
    if (this.heroInfo == null) {
      this.heroInfo = new java.util.ArrayList<PsHeroSimpleInfo>();
    }
    this.heroInfo.add(elem);
  }

  /**
   * 英雄 *
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<PsHeroSimpleInfo> getHeroInfo() {
    return this.heroInfo;
  }

  /**
   * 英雄 *
   */
  public PsArmySimpleInfo setHeroInfo(@org.apache.thrift.annotation.Nullable java.util.List<PsHeroSimpleInfo> heroInfo) {
    this.heroInfo = heroInfo;
    return this;
  }

  public void unsetHeroInfo() {
    this.heroInfo = null;
  }

  /** Returns true if field heroInfo is set (has been assigned a value) and false otherwise */
  public boolean isSetHeroInfo() {
    return this.heroInfo != null;
  }

  public void setHeroInfoIsSet(boolean value) {
    if (!value) {
      this.heroInfo = null;
    }
  }

  public int getSoldiersSize() {
    return (this.soldiers == null) ? 0 : this.soldiers.size();
  }

  public void putToSoldiers(java.lang.String key, int val) {
    if (this.soldiers == null) {
      this.soldiers = new java.util.HashMap<java.lang.String,java.lang.Integer>();
    }
    this.soldiers.put(key, val);
  }

  /**
   * 编队士兵列表
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.Map<java.lang.String,java.lang.Integer> getSoldiers() {
    return this.soldiers;
  }

  /**
   * 编队士兵列表
   */
  public PsArmySimpleInfo setSoldiers(@org.apache.thrift.annotation.Nullable java.util.Map<java.lang.String,java.lang.Integer> soldiers) {
    this.soldiers = soldiers;
    return this;
  }

  public void unsetSoldiers() {
    this.soldiers = null;
  }

  /** Returns true if field soldiers is set (has been assigned a value) and false otherwise */
  public boolean isSetSoldiers() {
    return this.soldiers != null;
  }

  public void setSoldiersIsSet(boolean value) {
    if (!value) {
      this.soldiers = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case HERO_INFO:
      if (value == null) {
        unsetHeroInfo();
      } else {
        setHeroInfo((java.util.List<PsHeroSimpleInfo>)value);
      }
      break;

    case SOLDIERS:
      if (value == null) {
        unsetSoldiers();
      } else {
        setSoldiers((java.util.Map<java.lang.String,java.lang.Integer>)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case HERO_INFO:
      return getHeroInfo();

    case SOLDIERS:
      return getSoldiers();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case HERO_INFO:
      return isSetHeroInfo();
    case SOLDIERS:
      return isSetSoldiers();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof PsArmySimpleInfo)
      return this.equals((PsArmySimpleInfo)that);
    return false;
  }

  public boolean equals(PsArmySimpleInfo that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_heroInfo = true && this.isSetHeroInfo();
    boolean that_present_heroInfo = true && that.isSetHeroInfo();
    if (this_present_heroInfo || that_present_heroInfo) {
      if (!(this_present_heroInfo && that_present_heroInfo))
        return false;
      if (!this.heroInfo.equals(that.heroInfo))
        return false;
    }

    boolean this_present_soldiers = true && this.isSetSoldiers();
    boolean that_present_soldiers = true && that.isSetSoldiers();
    if (this_present_soldiers || that_present_soldiers) {
      if (!(this_present_soldiers && that_present_soldiers))
        return false;
      if (!this.soldiers.equals(that.soldiers))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetHeroInfo()) ? 131071 : 524287);
    if (isSetHeroInfo())
      hashCode = hashCode * 8191 + heroInfo.hashCode();

    hashCode = hashCode * 8191 + ((isSetSoldiers()) ? 131071 : 524287);
    if (isSetSoldiers())
      hashCode = hashCode * 8191 + soldiers.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(PsArmySimpleInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetHeroInfo(), other.isSetHeroInfo());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetHeroInfo()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.heroInfo, other.heroInfo);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetSoldiers(), other.isSetSoldiers());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSoldiers()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.soldiers, other.soldiers);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("PsArmySimpleInfo(");
    boolean first = true;

    if (isSetHeroInfo()) {
      sb.append("heroInfo:");
      if (this.heroInfo == null) {
        sb.append("null");
      } else {
        sb.append(this.heroInfo);
      }
      first = false;
    }
    if (isSetSoldiers()) {
      if (!first) sb.append(", ");
      sb.append("soldiers:");
      if (this.soldiers == null) {
        sb.append("null");
      } else {
        sb.append(this.soldiers);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PsArmySimpleInfoStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsArmySimpleInfoStandardScheme getScheme() {
      return new PsArmySimpleInfoStandardScheme();
    }
  }

  private static class PsArmySimpleInfoStandardScheme extends org.apache.thrift.scheme.StandardScheme<PsArmySimpleInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PsArmySimpleInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // HERO_INFO
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.heroInfo = new java.util.ArrayList<PsHeroSimpleInfo>(_list0.size);
                @org.apache.thrift.annotation.Nullable PsHeroSimpleInfo _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new PsHeroSimpleInfo();
                  _elem1.read(iprot);
                  struct.heroInfo.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setHeroInfoIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // SOLDIERS
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map3 = iprot.readMapBegin();
                struct.soldiers = new java.util.HashMap<java.lang.String,java.lang.Integer>(2*_map3.size);
                @org.apache.thrift.annotation.Nullable java.lang.String _key4;
                int _val5;
                for (int _i6 = 0; _i6 < _map3.size; ++_i6)
                {
                  _key4 = iprot.readString();
                  _val5 = iprot.readI32();
                  struct.soldiers.put(_key4, _val5);
                }
                iprot.readMapEnd();
              }
              struct.setSoldiersIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PsArmySimpleInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.heroInfo != null) {
        if (struct.isSetHeroInfo()) {
          oprot.writeFieldBegin(HERO_INFO_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.heroInfo.size()));
            for (PsHeroSimpleInfo _iter7 : struct.heroInfo)
            {
              _iter7.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      if (struct.soldiers != null) {
        if (struct.isSetSoldiers()) {
          oprot.writeFieldBegin(SOLDIERS_FIELD_DESC);
          {
            oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.I32, struct.soldiers.size()));
            for (java.util.Map.Entry<java.lang.String, java.lang.Integer> _iter8 : struct.soldiers.entrySet())
            {
              oprot.writeString(_iter8.getKey());
              oprot.writeI32(_iter8.getValue());
            }
            oprot.writeMapEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PsArmySimpleInfoTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public PsArmySimpleInfoTupleScheme getScheme() {
      return new PsArmySimpleInfoTupleScheme();
    }
  }

  private static class PsArmySimpleInfoTupleScheme extends org.apache.thrift.scheme.TupleScheme<PsArmySimpleInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PsArmySimpleInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetHeroInfo()) {
        optionals.set(0);
      }
      if (struct.isSetSoldiers()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetHeroInfo()) {
        {
          oprot.writeI32(struct.heroInfo.size());
          for (PsHeroSimpleInfo _iter9 : struct.heroInfo)
          {
            _iter9.write(oprot);
          }
        }
      }
      if (struct.isSetSoldiers()) {
        {
          oprot.writeI32(struct.soldiers.size());
          for (java.util.Map.Entry<java.lang.String, java.lang.Integer> _iter10 : struct.soldiers.entrySet())
          {
            oprot.writeString(_iter10.getKey());
            oprot.writeI32(_iter10.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PsArmySimpleInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TList _list11 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
          struct.heroInfo = new java.util.ArrayList<PsHeroSimpleInfo>(_list11.size);
          @org.apache.thrift.annotation.Nullable PsHeroSimpleInfo _elem12;
          for (int _i13 = 0; _i13 < _list11.size; ++_i13)
          {
            _elem12 = new PsHeroSimpleInfo();
            _elem12.read(iprot);
            struct.heroInfo.add(_elem12);
          }
        }
        struct.setHeroInfoIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TMap _map14 = iprot.readMapBegin(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.I32); 
          struct.soldiers = new java.util.HashMap<java.lang.String,java.lang.Integer>(2*_map14.size);
          @org.apache.thrift.annotation.Nullable java.lang.String _key15;
          int _val16;
          for (int _i17 = 0; _i17 < _map14.size; ++_i17)
          {
            _key15 = iprot.readString();
            _val16 = iprot.readI32();
            struct.soldiers.put(_key15, _val16);
          }
        }
        struct.setSoldiersIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

