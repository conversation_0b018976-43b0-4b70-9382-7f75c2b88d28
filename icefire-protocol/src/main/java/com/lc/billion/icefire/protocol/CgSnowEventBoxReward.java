/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 冰雪节领宝箱
 * @Message(8264)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class CgSnowEventBoxReward implements org.apache.thrift.TBase<CgSnowEventBoxReward, CgSnowEventBoxReward._Fields>, java.io.Serializable, Cloneable, Comparable<CgSnowEventBoxReward> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("CgSnowEventBoxReward");

  private static final org.apache.thrift.protocol.TField ACTIVITY_META_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("activityMetaId", org.apache.thrift.protocol.TType.STRING, (short)1);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new CgSnowEventBoxRewardStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new CgSnowEventBoxRewardTupleSchemeFactory();

  /**
   * 活动id
   */
  public @org.apache.thrift.annotation.Nullable java.lang.String activityMetaId; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 活动id
     */
    ACTIVITY_META_ID((short)1, "activityMetaId");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ACTIVITY_META_ID
          return ACTIVITY_META_ID;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final _Fields optionals[] = {_Fields.ACTIVITY_META_ID};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ACTIVITY_META_ID, new org.apache.thrift.meta_data.FieldMetaData("activityMetaId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(CgSnowEventBoxReward.class, metaDataMap);
  }

  public CgSnowEventBoxReward() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public CgSnowEventBoxReward(CgSnowEventBoxReward other) {
    if (other.isSetActivityMetaId()) {
      this.activityMetaId = other.activityMetaId;
    }
  }

  public CgSnowEventBoxReward deepCopy() {
    return new CgSnowEventBoxReward(this);
  }

  @Override
  public void clear() {
    this.activityMetaId = null;
  }

  /**
   * 活动id
   */
  @org.apache.thrift.annotation.Nullable
  public java.lang.String getActivityMetaId() {
    return this.activityMetaId;
  }

  /**
   * 活动id
   */
  public CgSnowEventBoxReward setActivityMetaId(@org.apache.thrift.annotation.Nullable java.lang.String activityMetaId) {
    this.activityMetaId = activityMetaId;
    return this;
  }

  public void unsetActivityMetaId() {
    this.activityMetaId = null;
  }

  /** Returns true if field activityMetaId is set (has been assigned a value) and false otherwise */
  public boolean isSetActivityMetaId() {
    return this.activityMetaId != null;
  }

  public void setActivityMetaIdIsSet(boolean value) {
    if (!value) {
      this.activityMetaId = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ACTIVITY_META_ID:
      if (value == null) {
        unsetActivityMetaId();
      } else {
        setActivityMetaId((java.lang.String)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ACTIVITY_META_ID:
      return getActivityMetaId();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ACTIVITY_META_ID:
      return isSetActivityMetaId();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof CgSnowEventBoxReward)
      return this.equals((CgSnowEventBoxReward)that);
    return false;
  }

  public boolean equals(CgSnowEventBoxReward that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_activityMetaId = true && this.isSetActivityMetaId();
    boolean that_present_activityMetaId = true && that.isSetActivityMetaId();
    if (this_present_activityMetaId || that_present_activityMetaId) {
      if (!(this_present_activityMetaId && that_present_activityMetaId))
        return false;
      if (!this.activityMetaId.equals(that.activityMetaId))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetActivityMetaId()) ? 131071 : 524287);
    if (isSetActivityMetaId())
      hashCode = hashCode * 8191 + activityMetaId.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(CgSnowEventBoxReward other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetActivityMetaId(), other.isSetActivityMetaId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActivityMetaId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.activityMetaId, other.activityMetaId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("CgSnowEventBoxReward(");
    boolean first = true;

    if (isSetActivityMetaId()) {
      sb.append("activityMetaId:");
      if (this.activityMetaId == null) {
        sb.append("null");
      } else {
        sb.append(this.activityMetaId);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class CgSnowEventBoxRewardStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgSnowEventBoxRewardStandardScheme getScheme() {
      return new CgSnowEventBoxRewardStandardScheme();
    }
  }

  private static class CgSnowEventBoxRewardStandardScheme extends org.apache.thrift.scheme.StandardScheme<CgSnowEventBoxReward> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, CgSnowEventBoxReward struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ACTIVITY_META_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.activityMetaId = iprot.readString();
              struct.setActivityMetaIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, CgSnowEventBoxReward struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.activityMetaId != null) {
        if (struct.isSetActivityMetaId()) {
          oprot.writeFieldBegin(ACTIVITY_META_ID_FIELD_DESC);
          oprot.writeString(struct.activityMetaId);
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class CgSnowEventBoxRewardTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgSnowEventBoxRewardTupleScheme getScheme() {
      return new CgSnowEventBoxRewardTupleScheme();
    }
  }

  private static class CgSnowEventBoxRewardTupleScheme extends org.apache.thrift.scheme.TupleScheme<CgSnowEventBoxReward> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, CgSnowEventBoxReward struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetActivityMetaId()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetActivityMetaId()) {
        oprot.writeString(struct.activityMetaId);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, CgSnowEventBoxReward struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        struct.activityMetaId = iprot.readString();
        struct.setActivityMetaIdIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

