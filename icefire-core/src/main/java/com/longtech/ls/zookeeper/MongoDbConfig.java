package com.longtech.ls.zookeeper;

import com.longtech.cod.common.zookeeper.SimpleCurator;
import org.apache.curator.framework.recipes.cache.TreeCache;

/**
 * Mongo 数据库配置信息。
 * 
 * <AUTHOR>
 *
 */

public class MongoDbConfig extends ZkConfig {

	private String url;

	private String dbName;

	public String getUrl() {
		return url;
	}

	public String getDbName() {
		return dbName;
	}

	/**
	 * 有缓存版本初始化
	 * @param zk
	 * @param path
	 * @param cache
	 */
	public MongoDbConfig(SimpleCurator zk, String path, TreeCache cache) {
		super(zk, path);
		// mongo的配置必须预先存在；且配置改变必须重启game才能生效，因此不需要支持动态更新配置
		this.url = getDataFromCache(cache, "url");
		this.dbName = getDataFromCache(cache, "db_name");
	}

	@Override
	public String toString() {
		return "MongoDbConfig [url=" + url + ", dbName=" + dbName + ", getZkPath()=" + getZkPath() + "]";
	}

}
