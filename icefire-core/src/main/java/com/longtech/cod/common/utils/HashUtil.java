package com.longtech.cod.common.utils;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;


import org.apache.commons.codec.binary.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class HashUtil {

	private static final Logger logger = LoggerFactory.getLogger(HashUtil.class);

	public static String SHA1(String original) {
		String sha1 = null;
		try {
			MessageDigest msdDigest = MessageDigest.getInstance("SHA-1");
			msdDigest.update(original.getBytes("UTF-8"), 0, original.length());
			sha1 = Hex.encodeHexString(msdDigest.digest());
		} catch (UnsupportedEncodingException | NoSuchAlgorithmException e) {
			logger.error("SHA1", e);
		}
		return sha1.toLowerCase();
	}

	public static String SHA256(String original){
		String encodeStr = "";
		try {
			MessageDigest messageDigest = MessageDigest.getInstance("SHA-256");
			byte[] hash = messageDigest.digest(original.getBytes(StandardCharsets.UTF_8));
			encodeStr = Hex.encodeHexString(hash);
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		}
		return encodeStr;
	}

//	public static void main(String[] args) {
//		// echo -n "asdf1234#aa" | openssl dgst -sha1
//		String input = "asdf1234#aa";
//		System.out.println(String.format("SHA1(\"%s\")=%s", input, SHA1(input)));
//		System.out.println("98259fe07997db464c0f89fbdc5182cd22087e7d".equals(SHA1(input)));
//	}
}
